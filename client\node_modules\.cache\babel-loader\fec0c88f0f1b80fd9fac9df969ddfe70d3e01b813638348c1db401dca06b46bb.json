{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizSelection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbBrain, TbClock, TbQuestionMark, TbArrowRight, TbLoader } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { getTrialQuiz } from \"../../apicalls/trial\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizSelection = ({\n  trialUserInfo,\n  onQuizSelected,\n  onBack\n}) => {\n  _s();\n  var _quiz$exam, _quiz$exam2, _quiz$trialInfo, _quiz$exam3, _quiz$exam3$questions, _quiz$trialInfo2, _quiz$exam4, _quiz$exam5, _quiz$exam6, _quiz$exam6$questions, _quiz$trialInfo3;\n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchTrialQuiz();\n  }, [trialUserInfo]);\n  const fetchTrialQuiz = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await getTrialQuiz({\n        level: trialUserInfo.level,\n        class: trialUserInfo.class\n      });\n      if (response.success) {\n        setQuiz(response.data);\n        console.log(\"✅ Trial quiz loaded:\", response.data);\n      } else {\n        setError(response.message || \"Failed to load trial quiz\");\n        message.error(response.message || \"Failed to load trial quiz\");\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching trial quiz:\", error);\n      setError(\"Something went wrong. Please try again.\");\n      message.error(\"Something went wrong. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleStartQuiz = () => {\n    if (quiz && quiz.exam) {\n      onQuizSelected({\n        ...quiz,\n        trialUserInfo\n      });\n    }\n  };\n  const containerVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n  const cardVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 0.9\n    },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      transition: {\n        duration: 0.5,\n        delay: 0.2\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\",\n      variants: containerVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbLoader, {\n            className: \"w-8 h-8 text-blue-600 animate-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-2\",\n          children: \"Finding Your Perfect Trial Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"We're selecting a quiz that matches your level and class...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\",\n      variants: containerVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(TbQuestionMark, {\n            className: \"w-8 h-8 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-2\",\n          children: \"Quiz Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: fetchTrialQuiz,\n            className: \"w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onBack,\n            className: \"w-full py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n            children: \"Go Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\",\n    variants: containerVariants,\n    initial: \"hidden\",\n    animate: \"visible\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-2xl w-full mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center mb-6 sm:mb-8\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2\",\n          children: [\"Welcome, \", trialUserInfo.name, \"! \\uD83D\\uDC4B\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-base sm:text-lg text-gray-600 px-4\",\n          children: [\"We've found the perfect quiz for your \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-blue-600\",\n            children: [trialUserInfo.level, \" \", trialUserInfo.class]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 51\n          }, this), \" level\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n        variants: cardVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-6 sm:py-8 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 sm:space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 sm:p-3 bg-white/20 rounded-xl\",\n              children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-6 h-6 sm:w-8 sm:h-8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-w-0 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg sm:text-2xl font-bold truncate\",\n                children: quiz === null || quiz === void 0 ? void 0 : (_quiz$exam = quiz.exam) === null || _quiz$exam === void 0 ? void 0 : _quiz$exam.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-100 text-sm sm:text-base\",\n                children: [quiz === null || quiz === void 0 ? void 0 : (_quiz$exam2 = quiz.exam) === null || _quiz$exam2 === void 0 ? void 0 : _quiz$exam2.subject, \" \\u2022 \", trialUserInfo.level.charAt(0).toUpperCase() + trialUserInfo.level.slice(1), \" Level\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 sm:p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 bg-blue-50 rounded-xl\",\n              children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                className: \"w-8 h-8 text-blue-600 mx-auto mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-800\",\n                children: (quiz === null || quiz === void 0 ? void 0 : (_quiz$trialInfo = quiz.trialInfo) === null || _quiz$trialInfo === void 0 ? void 0 : _quiz$trialInfo.trialQuestionCount) || (quiz === null || quiz === void 0 ? void 0 : (_quiz$exam3 = quiz.exam) === null || _quiz$exam3 === void 0 ? void 0 : (_quiz$exam3$questions = _quiz$exam3.questions) === null || _quiz$exam3$questions === void 0 ? void 0 : _quiz$exam3$questions.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 bg-green-50 rounded-xl\",\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-8 h-8 text-green-600 mx-auto mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-800\",\n                children: (quiz === null || quiz === void 0 ? void 0 : (_quiz$trialInfo2 = quiz.trialInfo) === null || _quiz$trialInfo2 === void 0 ? void 0 : _quiz$trialInfo2.trialDuration) || (quiz === null || quiz === void 0 ? void 0 : (_quiz$exam4 = quiz.exam) === null || _quiz$exam4 === void 0 ? void 0 : _quiz$exam4.duration) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Minutes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 bg-purple-50 rounded-xl\",\n              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-8 h-8 text-purple-600 mx-auto mb-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-800\",\n                children: (quiz === null || quiz === void 0 ? void 0 : (_quiz$exam5 = quiz.exam) === null || _quiz$exam5 === void 0 ? void 0 : _quiz$exam5.passingMarks) || Math.ceil(((quiz === null || quiz === void 0 ? void 0 : (_quiz$exam6 = quiz.exam) === null || _quiz$exam6 === void 0 ? void 0 : (_quiz$exam6$questions = _quiz$exam6.questions) === null || _quiz$exam6$questions === void 0 ? void 0 : _quiz$exam6$questions.length) || 0) * 0.6)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Pass Mark\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-4 sm:p-6 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-blue-800 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg mr-2\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), \"Personalized Trial Experience\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-sm text-blue-700 space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600 mr-2\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Quiz specifically selected for \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [trialUserInfo.level, \" \", trialUserInfo.class]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 56\n                  }, this), \" students\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600 mr-2\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Limited to \", (quiz === null || quiz === void 0 ? void 0 : (_quiz$trialInfo3 = quiz.trialInfo) === null || _quiz$trialInfo3 === void 0 ? void 0 : _quiz$trialInfo3.trialQuestionCount) || 5, \" questions for trial experience\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600 mr-2\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Instant results with detailed explanations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-600 mr-2\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"No registration required - start immediately\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onBack,\n              className: \"flex-1 py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n              children: \"Go Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: handleStartQuiz,\n              className: \"flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\",\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Start Trial Quiz\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center mt-6 text-sm text-gray-500\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.8\n        },\n        children: \"After completing this trial, you'll be invited to register for full access to our platform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizSelection, \"2KLVUOffxt8hbnKtY8ZV71wmpE8=\");\n_c = TrialQuizSelection;\nexport default TrialQuizSelection;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizSelection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "TbBrain", "TbClock", "TbQuestionMark", "TbArrowRight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "getTrialQuiz", "jsxDEV", "_jsxDEV", "TrialQuizSelection", "trialUserInfo", "onQuizSelected", "onBack", "_s", "_quiz$exam", "_quiz$exam2", "_quiz$trialInfo", "_quiz$exam3", "_quiz$exam3$questions", "_quiz$trialInfo2", "_quiz$exam4", "_quiz$exam5", "_quiz$exam6", "_quiz$exam6$questions", "_quiz$trialInfo3", "loading", "setLoading", "quiz", "setQuiz", "error", "setError", "fetchTrialQuiz", "response", "level", "class", "success", "data", "console", "log", "handleStartQuiz", "exam", "containerVariants", "hidden", "opacity", "y", "visible", "transition", "duration", "ease", "cardVariants", "scale", "delay", "div", "className", "variants", "initial", "animate", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "name", "subject", "char<PERSON>t", "toUpperCase", "slice", "trialInfo", "trialQuestionCount", "questions", "length", "trialDuration", "passingMarks", "Math", "ceil", "button", "whileHover", "whileTap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizSelection.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbBrain, TbClock, TbQuestionMark, TbArrowRight, TbLoader } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { getTrialQuiz } from \"../../apicalls/trial\";\n\nconst TrialQuizSelection = ({ trialUserInfo, onQuizSelected, onBack }) => {\n  const [loading, setLoading] = useState(true);\n  const [quiz, setQuiz] = useState(null);\n  const [error, setError] = useState(null);\n\n  useEffect(() => {\n    fetchTrialQuiz();\n  }, [trialUserInfo]);\n\n  const fetchTrialQuiz = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await getTrialQuiz({\n        level: trialUserInfo.level,\n        class: trialUserInfo.class\n      });\n\n      if (response.success) {\n        setQuiz(response.data);\n        console.log(\"✅ Trial quiz loaded:\", response.data);\n      } else {\n        setError(response.message || \"Failed to load trial quiz\");\n        message.error(response.message || \"Failed to load trial quiz\");\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching trial quiz:\", error);\n      setError(\"Something went wrong. Please try again.\");\n      message.error(\"Something went wrong. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleStartQuiz = () => {\n    if (quiz && quiz.exam) {\n      onQuizSelected({\n        ...quiz,\n        trialUserInfo\n      });\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.9 },\n    visible: { \n      opacity: 1, \n      scale: 1,\n      transition: { duration: 0.5, delay: 0.2 }\n    }\n  };\n\n  if (loading) {\n    return (\n      <motion.div\n        className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center\">\n            <TbLoader className=\"w-8 h-8 text-blue-600 animate-spin\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">\n            Finding Your Perfect Trial Quiz\n          </h2>\n          <p className=\"text-gray-600\">\n            We're selecting a quiz that matches your level and class...\n          </p>\n        </div>\n      </motion.div>\n    );\n  }\n\n  if (error) {\n    return (\n      <motion.div\n        className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center\">\n            <TbQuestionMark className=\"w-8 h-8 text-red-600\" />\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">\n            Quiz Not Available\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            {error}\n          </p>\n          <div className=\"space-y-3\">\n            <button\n              onClick={fetchTrialQuiz}\n              className=\"w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\"\n            >\n              Try Again\n            </button>\n            <button\n              onClick={onBack}\n              className=\"w-full py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n            >\n              Go Back\n            </button>\n          </div>\n        </div>\n      </motion.div>\n    );\n  }\n\n  return (\n    <motion.div\n      className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      <div className=\"max-w-2xl w-full mx-auto\">\n        {/* Welcome Header */}\n        <motion.div\n          className=\"text-center mb-6 sm:mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2\">\n            Welcome, {trialUserInfo.name}! 👋\n          </h1>\n          <p className=\"text-base sm:text-lg text-gray-600 px-4\">\n            We've found the perfect quiz for your <span className=\"font-semibold text-blue-600\">{trialUserInfo.level} {trialUserInfo.class}</span> level\n          </p>\n        </motion.div>\n\n        {/* Quiz Card */}\n        <motion.div\n          className=\"bg-white rounded-2xl shadow-xl overflow-hidden\"\n          variants={cardVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Quiz Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-6 sm:py-8 text-white\">\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\n              <div className=\"p-2 sm:p-3 bg-white/20 rounded-xl\">\n                <TbBrain className=\"w-6 h-6 sm:w-8 sm:h-8\" />\n              </div>\n              <div className=\"min-w-0 flex-1\">\n                <h2 className=\"text-lg sm:text-2xl font-bold truncate\">{quiz?.exam?.name}</h2>\n                <p className=\"text-blue-100 text-sm sm:text-base\">\n                  {quiz?.exam?.subject} • {trialUserInfo.level.charAt(0).toUpperCase() + trialUserInfo.level.slice(1)} Level\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Quiz Details */}\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8\">\n              <div className=\"text-center p-4 bg-blue-50 rounded-xl\">\n                <TbQuestionMark className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.trialInfo?.trialQuestionCount || quiz?.exam?.questions?.length || 0}\n                </div>\n                <div className=\"text-sm text-gray-600\">Questions</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-green-50 rounded-xl\">\n                <TbClock className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.trialInfo?.trialDuration || quiz?.exam?.duration || 0}\n                </div>\n                <div className=\"text-sm text-gray-600\">Minutes</div>\n              </div>\n              \n              <div className=\"text-center p-4 bg-purple-50 rounded-xl\">\n                <TbBrain className=\"w-8 h-8 text-purple-600 mx-auto mb-2\" />\n                <div className=\"text-2xl font-bold text-gray-800\">\n                  {quiz?.exam?.passingMarks || Math.ceil((quiz?.exam?.questions?.length || 0) * 0.6)}\n                </div>\n                <div className=\"text-sm text-gray-600\">Pass Mark</div>\n              </div>\n            </div>\n\n            {/* Trial Info */}\n            <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-4 sm:p-6 mb-6\">\n              <h3 className=\"font-semibold text-blue-800 mb-3 flex items-center\">\n                <span className=\"text-lg mr-2\">🎯</span>\n                Personalized Trial Experience\n              </h3>\n              <ul className=\"text-sm text-blue-700 space-y-2\">\n                <li className=\"flex items-start\">\n                  <span className=\"text-green-600 mr-2\">✓</span>\n                  <span>Quiz specifically selected for <strong>{trialUserInfo.level} {trialUserInfo.class}</strong> students</span>\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"text-green-600 mr-2\">✓</span>\n                  <span>Limited to {quiz?.trialInfo?.trialQuestionCount || 5} questions for trial experience</span>\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"text-green-600 mr-2\">✓</span>\n                  <span>Instant results with detailed explanations</span>\n                </li>\n                <li className=\"flex items-start\">\n                  <span className=\"text-green-600 mr-2\">✓</span>\n                  <span>No registration required - start immediately</span>\n                </li>\n              </ul>\n            </div>\n\n\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              <button\n                onClick={onBack}\n                className=\"flex-1 py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n              >\n                Go Back\n              </button>\n              <motion.button\n                onClick={handleStartQuiz}\n                className=\"flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span>Start Trial Quiz</span>\n                <TbArrowRight className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Footer Note */}\n        <motion.div\n          className=\"text-center mt-6 text-sm text-gray-500\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.8 }}\n        >\n          After completing this trial, you'll be invited to register for full access to our platform\n        </motion.div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default TrialQuizSelection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,gBAAgB;AACzF,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,YAAY,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,aAAa;EAAEC,cAAc;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,eAAA,EAAAC,WAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,qBAAA,EAAAC,gBAAA;EACxE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACdiC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACrB,aAAa,CAAC,CAAC;EAEnB,MAAMqB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAME,QAAQ,GAAG,MAAM1B,YAAY,CAAC;QAClC2B,KAAK,EAAEvB,aAAa,CAACuB,KAAK;QAC1BC,KAAK,EAAExB,aAAa,CAACwB;MACvB,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,OAAO,EAAE;QACpBP,OAAO,CAACI,QAAQ,CAACI,IAAI,CAAC;QACtBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEN,QAAQ,CAACI,IAAI,CAAC;MACpD,CAAC,MAAM;QACLN,QAAQ,CAACE,QAAQ,CAAC3B,OAAO,IAAI,2BAA2B,CAAC;QACzDA,OAAO,CAACwB,KAAK,CAACG,QAAQ,CAAC3B,OAAO,IAAI,2BAA2B,CAAC;MAChE;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAAC,yCAAyC,CAAC;MACnDzB,OAAO,CAACwB,KAAK,CAAC,yCAAyC,CAAC;IAC1D,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIZ,IAAI,IAAIA,IAAI,CAACa,IAAI,EAAE;MACrB7B,cAAc,CAAC;QACb,GAAGgB,IAAI;QACPjB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM+B,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC7BC,OAAO,EAAE;MACPF,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,IAAI,EAAE;MAAU;IAC/C;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBP,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEO,KAAK,EAAE;IAAI,CAAC;IAClCL,OAAO,EAAE;MACPF,OAAO,EAAE,CAAC;MACVO,KAAK,EAAE,CAAC;MACRJ,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEI,KAAK,EAAE;MAAI;IAC1C;EACF,CAAC;EAED,IAAI1B,OAAO,EAAE;IACX,oBACEjB,OAAA,CAACT,MAAM,CAACqD,GAAG;MACTC,SAAS,EAAC,uGAAuG;MACjHC,QAAQ,EAAEb,iBAAkB;MAC5Bc,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,SAAS;MAAAC,QAAA,eAEjBjD,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAI,QAAA,gBAC1BjD,OAAA;UAAK6C,SAAS,EAAC,kFAAkF;UAAAI,QAAA,eAC/FjD,OAAA,CAACJ,QAAQ;YAACiD,SAAS,EAAC;UAAoC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACNrD,OAAA;UAAI6C,SAAS,EAAC,0CAA0C;UAAAI,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAG6C,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEjB;EAEA,IAAIhC,KAAK,EAAE;IACT,oBACErB,OAAA,CAACT,MAAM,CAACqD,GAAG;MACTC,SAAS,EAAC,uGAAuG;MACjHC,QAAQ,EAAEb,iBAAkB;MAC5Bc,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,SAAS;MAAAC,QAAA,eAEjBjD,OAAA;QAAK6C,SAAS,EAAC,gEAAgE;QAAAI,QAAA,gBAC7EjD,OAAA;UAAK6C,SAAS,EAAC,iFAAiF;UAAAI,QAAA,eAC9FjD,OAAA,CAACN,cAAc;YAACmD,SAAS,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNrD,OAAA;UAAI6C,SAAS,EAAC,0CAA0C;UAAAI,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAG6C,SAAS,EAAC,oBAAoB;UAAAI,QAAA,EAC9B5B;QAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJrD,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAI,QAAA,gBACxBjD,OAAA;YACEsD,OAAO,EAAE/B,cAAe;YACxBsB,SAAS,EAAC,oGAAoG;YAAAI,QAAA,EAC/G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrD,OAAA;YACEsD,OAAO,EAAElD,MAAO;YAChByC,SAAS,EAAC,iHAAiH;YAAAI,QAAA,EAC5H;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEjB;EAEA,oBACErD,OAAA,CAACT,MAAM,CAACqD,GAAG;IACTC,SAAS,EAAC,uGAAuG;IACjHC,QAAQ,EAAEb,iBAAkB;IAC5Bc,OAAO,EAAC,QAAQ;IAChBC,OAAO,EAAC,SAAS;IAAAC,QAAA,eAEjBjD,OAAA;MAAK6C,SAAS,EAAC,0BAA0B;MAAAI,QAAA,gBAEvCjD,OAAA,CAACT,MAAM,CAACqD,GAAG;QACTC,SAAS,EAAC,0BAA0B;QACpCE,OAAO,EAAE;UAAEZ,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCY,OAAO,EAAE;UAAEb,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAU,QAAA,gBAE9BjD,OAAA;UAAI6C,SAAS,EAAC,+DAA+D;UAAAI,QAAA,GAAC,WACnE,EAAC/C,aAAa,CAACqD,IAAI,EAAC,gBAC/B;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrD,OAAA;UAAG6C,SAAS,EAAC,yCAAyC;UAAAI,QAAA,GAAC,wCACf,eAAAjD,OAAA;YAAM6C,SAAS,EAAC,6BAA6B;YAAAI,QAAA,GAAE/C,aAAa,CAACuB,KAAK,EAAC,GAAC,EAACvB,aAAa,CAACwB,KAAK;UAAA;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,UACxI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbrD,OAAA,CAACT,MAAM,CAACqD,GAAG;QACTC,SAAS,EAAC,gDAAgD;QAC1DC,QAAQ,EAAEL,YAAa;QACvBM,OAAO,EAAC,QAAQ;QAChBC,OAAO,EAAC,SAAS;QAAAC,QAAA,gBAGjBjD,OAAA;UAAK6C,SAAS,EAAC,iFAAiF;UAAAI,QAAA,eAC9FjD,OAAA;YAAK6C,SAAS,EAAC,0CAA0C;YAAAI,QAAA,gBACvDjD,OAAA;cAAK6C,SAAS,EAAC,mCAAmC;cAAAI,QAAA,eAChDjD,OAAA,CAACR,OAAO;gBAACqD,SAAS,EAAC;cAAuB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNrD,OAAA;cAAK6C,SAAS,EAAC,gBAAgB;cAAAI,QAAA,gBAC7BjD,OAAA;gBAAI6C,SAAS,EAAC,wCAAwC;gBAAAI,QAAA,EAAE9B,IAAI,aAAJA,IAAI,wBAAAb,UAAA,GAAJa,IAAI,CAAEa,IAAI,cAAA1B,UAAA,uBAAVA,UAAA,CAAYiD;cAAI;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9ErD,OAAA;gBAAG6C,SAAS,EAAC,oCAAoC;gBAAAI,QAAA,GAC9C9B,IAAI,aAAJA,IAAI,wBAAAZ,WAAA,GAAJY,IAAI,CAAEa,IAAI,cAAAzB,WAAA,uBAAVA,WAAA,CAAYiD,OAAO,EAAC,UAAG,EAACtD,aAAa,CAACuB,KAAK,CAACgC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGxD,aAAa,CAACuB,KAAK,CAACkC,KAAK,CAAC,CAAC,CAAC,EAAC,QACtG;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAI,QAAA,gBACzBjD,OAAA;YAAK6C,SAAS,EAAC,6DAA6D;YAAAI,QAAA,gBAC1EjD,OAAA;cAAK6C,SAAS,EAAC,uCAAuC;cAAAI,QAAA,gBACpDjD,OAAA,CAACN,cAAc;gBAACmD,SAAS,EAAC;cAAoC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjErD,OAAA;gBAAK6C,SAAS,EAAC,kCAAkC;gBAAAI,QAAA,EAC9C,CAAA9B,IAAI,aAAJA,IAAI,wBAAAX,eAAA,GAAJW,IAAI,CAAEyC,SAAS,cAAApD,eAAA,uBAAfA,eAAA,CAAiBqD,kBAAkB,MAAI1C,IAAI,aAAJA,IAAI,wBAAAV,WAAA,GAAJU,IAAI,CAAEa,IAAI,cAAAvB,WAAA,wBAAAC,qBAAA,GAAVD,WAAA,CAAYqD,SAAS,cAAApD,qBAAA,uBAArBA,qBAAA,CAAuBqD,MAAM,KAAI;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACNrD,OAAA;gBAAK6C,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAENrD,OAAA;cAAK6C,SAAS,EAAC,wCAAwC;cAAAI,QAAA,gBACrDjD,OAAA,CAACP,OAAO;gBAACoD,SAAS,EAAC;cAAqC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3DrD,OAAA;gBAAK6C,SAAS,EAAC,kCAAkC;gBAAAI,QAAA,EAC9C,CAAA9B,IAAI,aAAJA,IAAI,wBAAAR,gBAAA,GAAJQ,IAAI,CAAEyC,SAAS,cAAAjD,gBAAA,uBAAfA,gBAAA,CAAiBqD,aAAa,MAAI7C,IAAI,aAAJA,IAAI,wBAAAP,WAAA,GAAJO,IAAI,CAAEa,IAAI,cAAApB,WAAA,uBAAVA,WAAA,CAAY2B,QAAQ,KAAI;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNrD,OAAA;gBAAK6C,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eAENrD,OAAA;cAAK6C,SAAS,EAAC,yCAAyC;cAAAI,QAAA,gBACtDjD,OAAA,CAACR,OAAO;gBAACqD,SAAS,EAAC;cAAsC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DrD,OAAA;gBAAK6C,SAAS,EAAC,kCAAkC;gBAAAI,QAAA,EAC9C,CAAA9B,IAAI,aAAJA,IAAI,wBAAAN,WAAA,GAAJM,IAAI,CAAEa,IAAI,cAAAnB,WAAA,uBAAVA,WAAA,CAAYoD,YAAY,KAAIC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAAhD,IAAI,aAAJA,IAAI,wBAAAL,WAAA,GAAJK,IAAI,CAAEa,IAAI,cAAAlB,WAAA,wBAAAC,qBAAA,GAAVD,WAAA,CAAYgD,SAAS,cAAA/C,qBAAA,uBAArBA,qBAAA,CAAuBgD,MAAM,KAAI,CAAC,IAAI,GAAG;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eACNrD,OAAA;gBAAK6C,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrD,OAAA;YAAK6C,SAAS,EAAC,8FAA8F;YAAAI,QAAA,gBAC3GjD,OAAA;cAAI6C,SAAS,EAAC,oDAAoD;cAAAI,QAAA,gBAChEjD,OAAA;gBAAM6C,SAAS,EAAC,cAAc;gBAAAI,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,iCAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrD,OAAA;cAAI6C,SAAS,EAAC,iCAAiC;cAAAI,QAAA,gBAC7CjD,OAAA;gBAAI6C,SAAS,EAAC,kBAAkB;gBAAAI,QAAA,gBAC9BjD,OAAA;kBAAM6C,SAAS,EAAC,qBAAqB;kBAAAI,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CrD,OAAA;kBAAAiD,QAAA,GAAM,iCAA+B,eAAAjD,OAAA;oBAAAiD,QAAA,GAAS/C,aAAa,CAACuB,KAAK,EAAC,GAAC,EAACvB,aAAa,CAACwB,KAAK;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,aAAS;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC,eACLrD,OAAA;gBAAI6C,SAAS,EAAC,kBAAkB;gBAAAI,QAAA,gBAC9BjD,OAAA;kBAAM6C,SAAS,EAAC,qBAAqB;kBAAAI,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CrD,OAAA;kBAAAiD,QAAA,GAAM,aAAW,EAAC,CAAA9B,IAAI,aAAJA,IAAI,wBAAAH,gBAAA,GAAJG,IAAI,CAAEyC,SAAS,cAAA5C,gBAAA,uBAAfA,gBAAA,CAAiB6C,kBAAkB,KAAI,CAAC,EAAC,iCAA+B;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eACLrD,OAAA;gBAAI6C,SAAS,EAAC,kBAAkB;gBAAAI,QAAA,gBAC9BjD,OAAA;kBAAM6C,SAAS,EAAC,qBAAqB;kBAAAI,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CrD,OAAA;kBAAAiD,QAAA,EAAM;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACLrD,OAAA;gBAAI6C,SAAS,EAAC,kBAAkB;gBAAAI,QAAA,gBAC9BjD,OAAA;kBAAM6C,SAAS,EAAC,qBAAqB;kBAAAI,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CrD,OAAA;kBAAAiD,QAAA,EAAM;gBAA4C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAKNrD,OAAA;YAAK6C,SAAS,EAAC,iCAAiC;YAAAI,QAAA,gBAC9CjD,OAAA;cACEsD,OAAO,EAAElD,MAAO;cAChByC,SAAS,EAAC,iHAAiH;cAAAI,QAAA,EAC5H;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrD,OAAA,CAACT,MAAM,CAAC6E,MAAM;cACZd,OAAO,EAAEvB,eAAgB;cACzBc,SAAS,EAAC,yNAAyN;cACnOwB,UAAU,EAAE;gBAAE3B,KAAK,EAAE;cAAK,CAAE;cAC5B4B,QAAQ,EAAE;gBAAE5B,KAAK,EAAE;cAAK,CAAE;cAAAO,QAAA,gBAE1BjD,OAAA;gBAAAiD,QAAA,EAAM;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7BrD,OAAA,CAACL,YAAY;gBAACkD,SAAS,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbrD,OAAA,CAACT,MAAM,CAACqD,GAAG;QACTC,SAAS,EAAC,wCAAwC;QAClDE,OAAO,EAAE;UAAEZ,OAAO,EAAE;QAAE,CAAE;QACxBa,OAAO,EAAE;UAAEb,OAAO,EAAE;QAAE,CAAE;QACxBG,UAAU,EAAE;UAAEK,KAAK,EAAE;QAAI,CAAE;QAAAM,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAChD,EAAA,CAhQIJ,kBAAkB;AAAAsE,EAAA,GAAlBtE,kBAAkB;AAkQxB,eAAeA,kBAAkB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}