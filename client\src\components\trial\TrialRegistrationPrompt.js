import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
import { 
  TbX, 
  TbBrain, 
  TbBook, 
  TbChartBar, 
  TbMessageCircle, 
  TbUsers,
  TbStar,
  TbArrowRight,
  TbCheck,
  TbInfinity,
  TbTrophy,
  TbBulb
} from "react-icons/tb";

const TrialRegistrationPrompt = ({ isOpen, onClose, trialResult }) => {
  const premiumFeatures = [
    {
      icon: TbInfinity,
      title: "Unlimited Quizzes",
      description: "Access thousands of quizzes across all subjects and levels",
      color: "text-blue-600",
      bg: "bg-blue-50"
    },
    {
      icon: TbBook,
      title: "Study Materials",
      description: "Comprehensive notes, videos, and resources for all subjects",
      color: "text-green-600",
      bg: "bg-green-50"
    },
    {
      icon: TbBrain,
      title: "AI Assistant",
      description: "Get personalized explanations and study recommendations",
      color: "text-purple-600",
      bg: "bg-purple-50"
    },
    {
      icon: Tb<PERSON>hartBar,
      title: "Ranking System",
      description: "Compete with students nationwide and track your progress",
      color: "text-orange-600",
      bg: "bg-orange-50"
    },
    {
      icon: TbMessageCircle,
      title: "Forum Access",
      description: "Ask questions and help other students in our community",
      color: "text-indigo-600",
      bg: "bg-indigo-50"
    },
    {
      icon: TbTrophy,
      title: "Achievements",
      description: "Earn badges and rewards for your learning milestones",
      color: "text-yellow-600",
      bg: "bg-yellow-50"
    }
  ];

  const benefits = [
    "Track your progress across all subjects",
    "Get detailed performance analytics",
    "Access past papers and exam preparation",
    "Join study groups and discussions",
    "Receive personalized study plans",
    "Get instant feedback and explanations"
  ];

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 50 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { 
        type: "spring", 
        damping: 25, 
        stiffness: 300 
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8, 
      y: 50,
      transition: { duration: 0.2 }
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        {/* Backdrop */}
        <motion.div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={onClose}
        />
        
        {/* Modal */}
        <motion.div
          className="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl overflow-hidden"
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-6 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90"></div>
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white/20 rounded-lg">
                    <TbStar className="w-6 h-6" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">Congratulations! 🎉</h2>
                    <p className="text-blue-100">You've completed your trial quiz</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                >
                  <TbX className="w-5 h-5" />
                </button>
              </div>

              {/* Trial Result Summary */}
              {trialResult && (
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">{trialResult.percentage}%</div>
                      <div className="text-sm text-blue-100">Score</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{trialResult.correctAnswers}/{trialResult.totalQuestions}</div>
                      <div className="text-sm text-blue-100">Correct</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{trialResult.passed ? 'PASSED' : 'FAILED'}</div>
                      <div className="text-sm text-blue-100">Result</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {/* Unlock Message */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <TbBulb className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-2">
                Ready to Unlock Your Full Potential?
              </h3>
              <p className="text-gray-600 text-lg">
                Join thousands of students who are already excelling with BrainWave
              </p>
            </div>

            {/* Premium Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
              {premiumFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * index }}
                  className={`p-4 rounded-xl border-2 border-gray-100 hover:border-blue-200 transition-all ${feature.bg}`}
                >
                  <feature.icon className={`w-8 h-8 ${feature.color} mb-3`} />
                  <h4 className="font-semibold text-gray-800 mb-2">{feature.title}</h4>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Benefits List */}
            <div className="bg-gray-50 rounded-xl p-6 mb-8">
              <h4 className="font-bold text-gray-800 mb-4 flex items-center">
                <TbCheck className="w-5 h-5 text-green-600 mr-2" />
                What You'll Get With Full Access:
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <TbCheck className="w-4 h-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Social Proof */}
            <div className="bg-blue-50 rounded-xl p-6 mb-8">
              <div className="flex items-center justify-center space-x-8 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">10,000+</div>
                  <div className="text-sm text-gray-600">Active Students</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">50,000+</div>
                  <div className="text-sm text-gray-600">Quizzes Completed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">95%</div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <Link to="/register" className="block">
                <motion.button
                  className="w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl flex items-center justify-center space-x-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span>Create Free Account</span>
                  <TbArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>

              <Link to="/login" className="block">
                <button className="w-full py-3 px-6 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all">
                  Already have an account? Sign In
                </button>
              </Link>

              <div className="text-center">
                <button
                  onClick={onClose}
                  className="text-gray-500 hover:text-gray-700 text-sm font-medium"
                >
                  Maybe later
                </button>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-6 text-center text-xs text-gray-500">
              <p>✅ Free to join • ✅ No credit card required • ✅ Cancel anytime</p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TrialRegistrationPrompt;
