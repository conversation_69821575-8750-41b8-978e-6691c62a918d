{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Suspense, lazy } from \"react\";\nimport \"./stylesheets/theme.css\";\nimport \"./stylesheets/alignments.css\";\nimport \"./stylesheets/textelements.css\";\nimport \"./stylesheets/form-elements.css\";\nimport \"./stylesheets/custom-components.css\";\nimport \"./stylesheets/layout.css\";\nimport \"./styles/modern.css\";\nimport \"./styles/animations.css\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Loader from \"./components/Loader\";\nimport { useSelector } from \"react-redux\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { ErrorBoundary } from \"./components/modern\";\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\n\n// Immediate load components (critical for initial render)\nimport Login from \"./pages/common/Login\";\nimport Register from \"./pages/common/Register\";\nimport Home from \"./pages/common/Home\";\n\n// Lazy load components for better performance\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = /*#__PURE__*/lazy(_c = () => import(\"./pages/user/Quiz\"));\n_c2 = Quiz;\nconst QuizStart = /*#__PURE__*/lazy(_c3 = () => import(\"./pages/user/Quiz/QuizStart\"));\n_c4 = QuizStart;\nconst QuizPlay = /*#__PURE__*/lazy(_c5 = () => import(\"./pages/user/Quiz/QuizPlay\"));\n_c6 = QuizPlay;\nconst QuizResult = /*#__PURE__*/lazy(_c7 = () => import(\"./pages/user/Quiz/QuizResult\"));\n_c8 = QuizResult;\nconst ModernQuizPage = /*#__PURE__*/lazy(_c9 = () => import(\"./pages/user/Quiz/ModernQuizPage\"));\n_c10 = ModernQuizPage;\nconst Exams = /*#__PURE__*/lazy(_c11 = () => import(\"./pages/admin/Exams\"));\n_c12 = Exams;\nconst AddEditExam = /*#__PURE__*/lazy(_c13 = () => import(\"./pages/admin/Exams/AddEditExam\"));\n_c14 = AddEditExam;\nconst Users = /*#__PURE__*/lazy(_c15 = () => import(\"./pages/admin/Users\"));\n_c16 = Users;\nconst AdminDashboard = /*#__PURE__*/lazy(_c17 = () => import(\"./pages/admin/Dashboard\"));\n_c18 = AdminDashboard;\nconst TrialPage = /*#__PURE__*/lazy(_c19 = () => import(\"./pages/trial/TrialPage\"));\n_c20 = TrialPage;\nconst WriteExam = /*#__PURE__*/lazy(_c21 = () => import(\"./pages/user/WriteExam\"));\n_c22 = WriteExam;\nconst UserReports = /*#__PURE__*/lazy(_c23 = () => import(\"./pages/user/UserReports\"));\n_c24 = UserReports;\nconst AdminReports = /*#__PURE__*/lazy(_c25 = () => import(\"./pages/admin/AdminReports\"));\n_c26 = AdminReports;\nconst StudyMaterial = /*#__PURE__*/lazy(_c27 = () => import(\"./pages/user/StudyMaterial\"));\n_c28 = StudyMaterial;\nconst Ranking = /*#__PURE__*/lazy(_c29 = () => import(\"./pages/user/Ranking\"));\n_c30 = Ranking;\nconst RankingErrorBoundary = /*#__PURE__*/lazy(_c31 = () => import(\"./components/RankingErrorBoundary\"));\n_c32 = RankingErrorBoundary;\nconst Profile = /*#__PURE__*/lazy(_c33 = () => import(\"./pages/common/Profile\"));\n_c34 = Profile;\nconst AboutUs = /*#__PURE__*/lazy(_c35 = () => import(\"./pages/user/AboutUs\"));\n_c36 = AboutUs;\nconst Forum = /*#__PURE__*/lazy(_c37 = () => import(\"./pages/common/Forum\"));\n_c38 = Forum;\nconst Test = /*#__PURE__*/lazy(_c39 = () => import(\"./pages/user/Test\"));\n_c40 = Test;\nconst Chat = /*#__PURE__*/lazy(_c41 = () => import(\"./pages/user/Chat\"));\n_c42 = Chat;\nconst Plans = /*#__PURE__*/lazy(_c43 = () => import(\"./pages/user/Plans/Plans\"));\n_c44 = Plans;\nconst Hub = /*#__PURE__*/lazy(_c45 = () => import(\"./pages/user/Hub\"));\n_c46 = Hub;\nconst AdminStudyMaterials = /*#__PURE__*/lazy(_c47 = () => import(\"./pages/admin/StudyMaterials\"));\n_c48 = AdminStudyMaterials;\nconst AdminNotifications = /*#__PURE__*/lazy(_c49 = () => import(\"./pages/admin/Notifications/AdminNotifications\"));\n_c50 = AdminNotifications;\nconst DebugAuth = /*#__PURE__*/lazy(_c51 = () => import(\"./components/DebugAuth\"));\n_c52 = DebugAuth;\nconst RankingDemo = /*#__PURE__*/lazy(_c53 = () => import(\"./components/modern/RankingDemo\"));\n\n// Global error handler for CSS style errors\n_c54 = RankingDemo;\nwindow.addEventListener('error', event => {\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Error caught and handled:', event.message);\n    event.preventDefault();\n    return false;\n  }\n});\n\n// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection', event => {\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\n    event.preventDefault();\n  }\n});\n// Fast loading component for lazy routes\nconst FastLoader = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 font-medium\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 70,\n  columnNumber: 3\n}, this);\n_c55 = FastLoader;\nfunction App() {\n  _s();\n  const {\n    loading\n  } = useSelector(state => state.loader);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ranking-demo\",\n            element: /*#__PURE__*/_jsxDEV(RankingDemo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/trial\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(TrialPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/test\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/forum\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Forum, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/chat\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/plans\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Plans, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/hub\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Hub, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/quiz\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Quiz, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/modern-quiz\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(ModernQuizPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/write-exam/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(WriteExam, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/start\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizStart, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/play\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/result\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizResult, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(UserReports, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/study-material\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(StudyMaterial, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/ranking\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(RankingErrorBoundary, {\n                children: /*#__PURE__*/_jsxDEV(Ranking, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/about-us\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/users\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/add\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/edit/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/study-materials\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminStudyMaterials, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/notifications\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminNotifications, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/debug\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(DebugAuth, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c56 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56;\n$RefreshReg$(_c, \"Quiz$lazy\");\n$RefreshReg$(_c2, \"Quiz\");\n$RefreshReg$(_c3, \"QuizStart$lazy\");\n$RefreshReg$(_c4, \"QuizStart\");\n$RefreshReg$(_c5, \"QuizPlay$lazy\");\n$RefreshReg$(_c6, \"QuizPlay\");\n$RefreshReg$(_c7, \"QuizResult$lazy\");\n$RefreshReg$(_c8, \"QuizResult\");\n$RefreshReg$(_c9, \"ModernQuizPage$lazy\");\n$RefreshReg$(_c10, \"ModernQuizPage\");\n$RefreshReg$(_c11, \"Exams$lazy\");\n$RefreshReg$(_c12, \"Exams\");\n$RefreshReg$(_c13, \"AddEditExam$lazy\");\n$RefreshReg$(_c14, \"AddEditExam\");\n$RefreshReg$(_c15, \"Users$lazy\");\n$RefreshReg$(_c16, \"Users\");\n$RefreshReg$(_c17, \"AdminDashboard$lazy\");\n$RefreshReg$(_c18, \"AdminDashboard\");\n$RefreshReg$(_c19, \"TrialPage$lazy\");\n$RefreshReg$(_c20, \"TrialPage\");\n$RefreshReg$(_c21, \"WriteExam$lazy\");\n$RefreshReg$(_c22, \"WriteExam\");\n$RefreshReg$(_c23, \"UserReports$lazy\");\n$RefreshReg$(_c24, \"UserReports\");\n$RefreshReg$(_c25, \"AdminReports$lazy\");\n$RefreshReg$(_c26, \"AdminReports\");\n$RefreshReg$(_c27, \"StudyMaterial$lazy\");\n$RefreshReg$(_c28, \"StudyMaterial\");\n$RefreshReg$(_c29, \"Ranking$lazy\");\n$RefreshReg$(_c30, \"Ranking\");\n$RefreshReg$(_c31, \"RankingErrorBoundary$lazy\");\n$RefreshReg$(_c32, \"RankingErrorBoundary\");\n$RefreshReg$(_c33, \"Profile$lazy\");\n$RefreshReg$(_c34, \"Profile\");\n$RefreshReg$(_c35, \"AboutUs$lazy\");\n$RefreshReg$(_c36, \"AboutUs\");\n$RefreshReg$(_c37, \"Forum$lazy\");\n$RefreshReg$(_c38, \"Forum\");\n$RefreshReg$(_c39, \"Test$lazy\");\n$RefreshReg$(_c40, \"Test\");\n$RefreshReg$(_c41, \"Chat$lazy\");\n$RefreshReg$(_c42, \"Chat\");\n$RefreshReg$(_c43, \"Plans$lazy\");\n$RefreshReg$(_c44, \"Plans\");\n$RefreshReg$(_c45, \"Hub$lazy\");\n$RefreshReg$(_c46, \"Hub\");\n$RefreshReg$(_c47, \"AdminStudyMaterials$lazy\");\n$RefreshReg$(_c48, \"AdminStudyMaterials\");\n$RefreshReg$(_c49, \"AdminNotifications$lazy\");\n$RefreshReg$(_c50, \"AdminNotifications\");\n$RefreshReg$(_c51, \"DebugAuth$lazy\");\n$RefreshReg$(_c52, \"DebugAuth\");\n$RefreshReg$(_c53, \"RankingDemo$lazy\");\n$RefreshReg$(_c54, \"RankingDemo\");\n$RefreshReg$(_c55, \"FastLoader\");\n$RefreshReg$(_c56, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "ProtectedRoute", "Loader", "useSelector", "ThemeProvider", "Error<PERSON>ou<PERSON><PERSON>", "AdminProtectedRoute", "<PERSON><PERSON>", "Register", "Home", "jsxDEV", "_jsxDEV", "Quiz", "_c", "_c2", "QuizStart", "_c3", "_c4", "QuizPlay", "_c5", "_c6", "QuizResult", "_c7", "_c8", "ModernQuizPage", "_c9", "_c10", "<PERSON><PERSON>", "_c11", "_c12", "AddEditExam", "_c13", "_c14", "Users", "_c15", "_c16", "AdminDashboard", "_c17", "_c18", "TrialPage", "_c19", "_c20", "WriteExam", "_c21", "_c22", "UserReports", "_c23", "_c24", "AdminReports", "_c25", "_c26", "StudyMaterial", "_c27", "_c28", "Ranking", "_c29", "_c30", "RankingError<PERSON><PERSON><PERSON>ry", "_c31", "_c32", "Profile", "_c33", "_c34", "AboutUs", "_c35", "_c36", "Forum", "_c37", "_c38", "Test", "_c39", "_c40", "Cha<PERSON>", "_c41", "_c42", "Plans", "_c43", "_c44", "<PERSON><PERSON>", "_c45", "_c46", "AdminStudyMaterials", "_c47", "_c48", "AdminNotifications", "_c49", "_c50", "DebugAuth", "_c51", "_c52", "RankingDemo", "_c53", "_c54", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "FastLoader", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c55", "App", "_s", "loading", "state", "loader", "path", "element", "fallback", "_c56", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense, lazy } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\r\n\r\n// Immediate load components (critical for initial render)\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport Home from \"./pages/common/Home\";\r\n\r\n// Lazy load components for better performance\r\nconst Quiz = lazy(() => import(\"./pages/user/Quiz\"));\r\nconst QuizStart = lazy(() => import(\"./pages/user/Quiz/QuizStart\"));\r\nconst QuizPlay = lazy(() => import(\"./pages/user/Quiz/QuizPlay\"));\r\nconst QuizResult = lazy(() => import(\"./pages/user/Quiz/QuizResult\"));\r\nconst ModernQuizPage = lazy(() => import(\"./pages/user/Quiz/ModernQuizPage\"));\r\nconst Exams = lazy(() => import(\"./pages/admin/Exams\"));\r\nconst AddEditExam = lazy(() => import(\"./pages/admin/Exams/AddEditExam\"));\r\nconst Users = lazy(() => import(\"./pages/admin/Users\"));\r\nconst AdminDashboard = lazy(() => import(\"./pages/admin/Dashboard\"));\r\nconst TrialPage = lazy(() => import(\"./pages/trial/TrialPage\"));\r\nconst WriteExam = lazy(() => import(\"./pages/user/WriteExam\"));\r\nconst UserReports = lazy(() => import(\"./pages/user/UserReports\"));\r\nconst AdminReports = lazy(() => import(\"./pages/admin/AdminReports\"));\r\nconst StudyMaterial = lazy(() => import(\"./pages/user/StudyMaterial\"));\r\nconst Ranking = lazy(() => import(\"./pages/user/Ranking\"));\r\nconst RankingErrorBoundary = lazy(() => import(\"./components/RankingErrorBoundary\"));\r\nconst Profile = lazy(() => import(\"./pages/common/Profile\"));\r\nconst AboutUs = lazy(() => import(\"./pages/user/AboutUs\"));\r\nconst Forum = lazy(() => import(\"./pages/common/Forum\"));\r\nconst Test = lazy(() => import(\"./pages/user/Test\"));\r\nconst Chat = lazy(() => import(\"./pages/user/Chat\"));\r\nconst Plans = lazy(() => import(\"./pages/user/Plans/Plans\"));\r\nconst Hub = lazy(() => import(\"./pages/user/Hub\"));\r\nconst AdminStudyMaterials = lazy(() => import(\"./pages/admin/StudyMaterials\"));\r\nconst AdminNotifications = lazy(() => import(\"./pages/admin/Notifications/AdminNotifications\"));\r\nconst DebugAuth = lazy(() => import(\"./components/DebugAuth\"));\r\nconst RankingDemo = lazy(() => import(\"./components/modern/RankingDemo\"));\r\n\r\n// Global error handler for CSS style errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && event.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && event.reason.message.includes('Indexed property setter is not supported')) {\r\n    console.warn('CSS Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// Fast loading component for lazy routes\r\nconst FastLoader = () => (\r\n  <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n    <div className=\"text-center\">\r\n      <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n      <p className=\"text-gray-600 font-medium\">Loading...</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        {loading && <Loader />}\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n\r\n          {/* Trial Route (No authentication required) */}\r\n          <Route path=\"/trial\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <TrialPage />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Forum />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/chat\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Chat />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/plans\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Plans />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Hub />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Quiz />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/modern-quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <ModernQuizPage />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <WriteExam />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n          <Route\r\n            path=\"/quiz/:id/start\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizStart />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizResult />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <UserReports />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <StudyMaterial />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <RankingErrorBoundary>\r\n                  <Ranking />\r\n                </RankingErrorBoundary>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/about-us\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AboutUs />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminDashboard />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Users />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Exams />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminStudyMaterials />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminReports />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/notifications\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminNotifications />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/debug\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <DebugAuth />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AAC7C,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAO,gCAAgC;AACvC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAC5C,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,IAAI,MAAM,qBAAqB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,gBAAGf,IAAI,CAAAgB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA/CF,IAAI;AACV,MAAMG,SAAS,gBAAGlB,IAAI,CAAAmB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAACC,GAAA,GAA9DF,SAAS;AACf,MAAMG,QAAQ,gBAAGrB,IAAI,CAAAsB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAA5DF,QAAQ;AACd,MAAMG,UAAU,gBAAGxB,IAAI,CAAAyB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,GAAA,GAAhEF,UAAU;AAChB,MAAMG,cAAc,gBAAG3B,IAAI,CAAA4B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC;AAACC,IAAA,GAAxEF,cAAc;AACpB,MAAMG,KAAK,gBAAG9B,IAAI,CAAA+B,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AACX,MAAMG,WAAW,gBAAGjC,IAAI,CAAAkC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,KAAK,gBAAGpC,IAAI,CAAAqC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AACX,MAAMG,cAAc,gBAAGvC,IAAI,CAAAwC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA/DF,cAAc;AACpB,MAAMG,SAAS,gBAAG1C,IAAI,CAAA2C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA1DF,SAAS;AACf,MAAMG,SAAS,gBAAG7C,IAAI,CAAA8C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAGhD,IAAI,CAAAiD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAA7DF,WAAW;AACjB,MAAMG,YAAY,gBAAGnD,IAAI,CAAAoD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAhEF,YAAY;AAClB,MAAMG,aAAa,gBAAGtD,IAAI,CAAAuD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAjEF,aAAa;AACnB,MAAMG,OAAO,gBAAGzD,IAAI,CAAA0D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,oBAAoB,gBAAG5D,IAAI,CAAA6D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAA/EF,oBAAoB;AAC1B,MAAMG,OAAO,gBAAG/D,IAAI,CAAAgE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAvDF,OAAO;AACb,MAAMG,OAAO,gBAAGlE,IAAI,CAAAmE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,KAAK,gBAAGrE,IAAI,CAAAsE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAnDF,KAAK;AACX,MAAMG,IAAI,gBAAGxE,IAAI,CAAAyE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AACV,MAAMG,IAAI,gBAAG3E,IAAI,CAAA4E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AACV,MAAMG,KAAK,gBAAG9E,IAAI,CAAA+E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAAvDF,KAAK;AACX,MAAMG,GAAG,gBAAGjF,IAAI,CAAAkF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAA7CF,GAAG;AACT,MAAMG,mBAAmB,gBAAGpF,IAAI,CAAAqF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,IAAA,GAAzEF,mBAAmB;AACzB,MAAMG,kBAAkB,gBAAGvF,IAAI,CAAAwF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAAC;AAACC,IAAA,GAA1FF,kBAAkB;AACxB,MAAMG,SAAS,gBAAG1F,IAAI,CAAA2F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAG7F,IAAI,CAAA8F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;;AAEzE;AAAAC,IAAA,GAFMF,WAAW;AAGjBG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACvFC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEJ,KAAK,CAACC,OAAO,CAAC;IAClED,KAAK,CAACK,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvD,IAAIA,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,IAAID,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,EAAE;IACrHC,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC;IACrFD,KAAK,CAACK,cAAc,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF;AACA,MAAME,UAAU,GAAGA,CAAA,kBACjB3F,OAAA;EAAK4F,SAAS,EAAC,4FAA4F;EAAAC,QAAA,eACzG7F,OAAA;IAAK4F,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1B7F,OAAA;MAAK4F,SAAS,EAAC;IAA6E;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnGjG,OAAA;MAAG4F,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,IAAA,GAPIP,UAAU;AAShB,SAASQ,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAQ,CAAC,GAAG7G,WAAW,CAAE8G,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACxD,oBACEvG,OAAA,CAACN,aAAa;IAAAmG,QAAA,eACZ7F,OAAA,CAACP,aAAa;MAAAoG,QAAA,GACXQ,OAAO,iBAAIrG,OAAA,CAACT,MAAM;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtBjG,OAAA,CAACb,aAAa;QAAA0G,QAAA,eACd7F,OAAA,CAACZ,MAAM;UAAAyG,QAAA,gBAEL7F,OAAA,CAACX,KAAK;YAACmH,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEzG,OAAA,CAACJ,KAAK;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CjG,OAAA,CAACX,KAAK;YAACmH,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEzG,OAAA,CAACH,QAAQ;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDjG,OAAA,CAACX,KAAK;YAACmH,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEzG,OAAA,CAACF,IAAI;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCjG,OAAA,CAACX,KAAK;YAACmH,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEzG,OAAA,CAAC+E,WAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGxDjG,OAAA,CAACX,KAAK;YAACmH,IAAI,EAAC,QAAQ;YAACC,OAAO,eAC1BzG,OAAA,CAACf,QAAQ;cAACyH,QAAQ,eAAE1G,OAAA,CAAC2F,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjC7F,OAAA,CAAC4B,SAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJjG,OAAA,CAACX,KAAK;YAACmH,IAAI,EAAC,OAAO;YAACC,OAAO,eACzBzG,OAAA,CAACf,QAAQ;cAACyH,QAAQ,eAAE1G,OAAA,CAAC2F,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjC7F,OAAA,CAAC0D,IAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACf,QAAQ;gBAACyH,QAAQ,eAAE1G,OAAA,CAAC2F,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC7F,OAAA,CAACuD,KAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,UAAU;YACfC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACf,QAAQ;gBAACyH,QAAQ,eAAE1G,OAAA,CAAC2F,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC7F,OAAA,CAACiD,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACf,QAAQ;gBAACyH,QAAQ,eAAE1G,OAAA,CAAC2F,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC7F,OAAA,CAACiD,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAAC6D,IAAI;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,aAAa;YAClBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACgE,KAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACmE,GAAG;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACC,IAAI;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACa,cAAc;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAAC+B,SAAS;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,iBAAiB;YACtBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACI,SAAS;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACO,QAAQ;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACU,UAAU;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACkC,WAAW;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACwC,aAAa;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAAC8C,oBAAoB;gBAAA+C,QAAA,eACnB7F,OAAA,CAAC2C,OAAO;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACoD,OAAO;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACyB,cAAc;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACsB,KAAK;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACgB,KAAK;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACmB,WAAW;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,uBAAuB;YAC5BC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACmB,WAAW;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACsE,mBAAmB;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACqC,YAAY;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAACyE,kBAAkB;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEFjG,OAAA,CAACX,KAAK;YACJmH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLzG,OAAA,CAACV,cAAc;cAAAuG,QAAA,eACb7F,OAAA,CAACL,mBAAmB;gBAAAkG,QAAA,eAClB7F,OAAA,CAAC4E,SAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACG,EAAA,CA7QQD,GAAG;EAAA,QACU3G,WAAW;AAAA;AAAAmH,IAAA,GADxBR,GAAG;AA+QZ,eAAeA,GAAG;AAAC,IAAAjG,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAiB,IAAA,EAAAS,IAAA;AAAAC,YAAA,CAAA1G,EAAA;AAAA0G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA3C,IAAA;AAAA2C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAxC,IAAA;AAAAwC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAArC,IAAA;AAAAqC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAlC,IAAA;AAAAkC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAA/B,IAAA;AAAA+B,YAAA,CAAA9B,IAAA;AAAA8B,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAA3B,IAAA;AAAA2B,YAAA,CAAAV,IAAA;AAAAU,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}