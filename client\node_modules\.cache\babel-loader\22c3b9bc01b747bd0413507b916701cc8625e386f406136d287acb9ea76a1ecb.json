{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { TbTrophy, TbCheck, TbX, TbClock, TbBrain, TbArrowRight, TbStar, TbUsers, TbBook, TbMessageCircle, TbChartBar } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizResult = ({\n  result,\n  onTryAnother,\n  onRegister\n}) => {\n  _s();\n  var _result$questionResul;\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n  const getPerformanceMessage = percentage => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n  const premiumFeatures = [{\n    icon: TbBook,\n    title: \"Study Materials\",\n    description: \"Access comprehensive study materials, notes, and resources\"\n  }, {\n    icon: TbBrain,\n    title: \"AI Assistant\",\n    description: \"Get personalized explanations and study recommendations\"\n  }, {\n    icon: TbChartBar,\n    title: \"Ranking System\",\n    description: \"Compete with other students and track your progress\"\n  }, {\n    icon: TbMessageCircle,\n    title: \"Forum Access\",\n    description: \"Ask questions and help other students in our community\"\n  }, {\n    icon: TbUsers,\n    title: \"Unlimited Quizzes\",\n    description: \"Take as many quizzes as you want across all subjects\"\n  }, {\n    icon: TbStar,\n    title: \"Progress Tracking\",\n    description: \"Detailed analytics and performance insights\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-6 sm:py-12 px-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          ease: \"easeOut\"\n        },\n        className: \"text-center mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            scale: 0\n          },\n          animate: {\n            scale: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.3,\n            type: \"spring\",\n            stiffness: 200\n          },\n          className: `inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`,\n          onAnimationComplete: () => setAnimationComplete(true),\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.6\n          },\n          className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\",\n          children: \"Quiz Complete! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.8\n          },\n          className: `inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`,\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-xl sm:text-2xl font-bold ${performance.color}`,\n            children: performance.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.8\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.6,\n          delay: 1.0\n        },\n        className: \"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                scale: 0\n              },\n              animate: {\n                scale: 1\n              },\n              transition: {\n                duration: 0.8,\n                delay: 1.2,\n                type: \"spring\"\n              },\n              className: \"text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2\",\n              children: [result.percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white/90 text-lg sm:text-xl\",\n              children: \"Your Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\",\n            children: [{\n              label: \"Total Questions\",\n              value: result.totalQuestions,\n              icon: TbBook,\n              color: \"blue\",\n              delay: 1.4\n            }, {\n              label: \"Correct Answers\",\n              value: result.correctAnswers,\n              icon: TbCheck,\n              color: \"green\",\n              delay: 1.6\n            }, {\n              label: \"Wrong Answers\",\n              value: result.wrongAnswers,\n              icon: TbX,\n              color: \"red\",\n              delay: 1.8\n            }, {\n              label: \"Time Taken\",\n              value: formatTime(result.timeSpent),\n              icon: TbClock,\n              color: \"purple\",\n              delay: 2.0\n            }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: stat.delay\n              },\n              className: `p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`,\n                children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                  className: `w-6 h-6 text-${stat.color}-600`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`,\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 2.2\n            },\n            className: \"text-center mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${isPassed ? 'bg-green-100 text-green-700 border-2 border-green-200' : 'bg-red-100 text-red-700 border-2 border-red-200'}`,\n              children: [isPassed ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: 2.4\n            },\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowDetails(!showDetails),\n              className: \"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(TbChartBar, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: showDetails ? 'Hide Question Summary' : 'View Question Summary'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 2.6\n        },\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            className: \"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\",\n            whileHover: {\n              scale: 1.05,\n              y: -2\n            },\n            whileTap: {\n              scale: 0.98\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Register Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 2.8\n        },\n        className: \"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl sm:text-3xl font-bold mb-2\",\n            children: \"\\uD83D\\uDD13 Unlock These Amazing Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-lg\",\n            children: \"Join thousands of students already excelling with BrainWave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 sm:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n            children: premiumFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: 3.0 + 0.1 * index\n              },\n              className: \"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                  children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                    className: \"w-6 h-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-bold text-gray-800\",\n                  children: feature.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 leading-relaxed\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 flex items-center text-green-600 font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-5 h-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"Included in Free Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 3.6\n            },\n            className: \"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold text-gray-800 mb-4 text-center\",\n              children: \"\\uD83C\\uDFAF Why Students Choose BrainWave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n              children: [{\n                icon: \"🚀\",\n                title: \"Instant Access\",\n                desc: \"Start learning immediately\"\n              }, {\n                icon: \"📱\",\n                title: \"Mobile Friendly\",\n                desc: \"Study anywhere, anytime\"\n              }, {\n                icon: \"🎓\",\n                title: \"Expert Content\",\n                desc: \"Created by top educators\"\n              }, {\n                icon: \"🏆\",\n                title: \"Proven Results\",\n                desc: \"98% success rate\"\n              }].map((benefit, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.4,\n                  delay: 3.8 + 0.1 * index\n                },\n                className: \"text-center p-4 bg-white rounded-xl shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl mb-2\",\n                  children: benefit.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-gray-800 mb-1\",\n                  children: benefit.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: benefit.desc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 4.2\n            },\n            className: \"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xl font-bold mb-2\",\n              children: \"Ready to Excel? \\uD83C\\uDF1F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 mb-4\",\n              children: \"Join BrainWave today and unlock your full potential!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-3 justify-center items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  className: \"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\",\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Create Free Account\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-blue-200 text-sm\",\n                children: \"\\u2728 No credit card required \\u2022 Start immediately\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          height: 0\n        },\n        animate: {\n          opacity: 1,\n          height: \"auto\"\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-800 mb-4\",\n          children: \"Question Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: (_result$questionResul = result.questionResults) === null || _result$questionResul === void 0 ? void 0 : _result$questionResul.map((q, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-4 rounded-lg border-2 ${q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center ${q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                children: q.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 38\n                }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-800 mb-2\",\n                  children: q.question\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Your answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`,\n                      children: q.userAnswer || 'Not answered'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 410,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 25\n                  }, this), !q.isCorrect && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Correct answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2 font-medium text-green-600\",\n                      children: q.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.6\n        },\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onTryAnother,\n          className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n          children: \"Try Another Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\",\n            children: \"Back to Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\",\n          children: \"\\uD83C\\uDFAF Trial Mode - Register for unlimited access\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizResult, \"4yqwfDzoJfQxBqvC4gGtnTfiTt0=\");\n_c = TrialQuizResult;\nexport default TrialQuizResult;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizResult\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "TbTrophy", "TbCheck", "TbX", "TbClock", "TbBrain", "TbArrowRight", "TbStar", "TbUsers", "TbBook", "TbMessageCircle", "TbChartBar", "jsxDEV", "_jsxDEV", "TrialQuizResult", "result", "onTryAnother", "onRegister", "_s", "_result$questionResul", "showDetails", "setShowDetails", "animationComplete", "setAnimationComplete", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "getPerformanceMessage", "percentage", "message", "color", "bg", "gradient", "performance", "isPassed", "premiumFeatures", "icon", "title", "description", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "ease", "scale", "delay", "type", "stiffness", "onAnimationComplete", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "h1", "split", "label", "value", "totalQuestions", "correctAnswers", "wrongAnswers", "timeSpent", "map", "stat", "index", "onClick", "to", "button", "whileHover", "whileTap", "feature", "desc", "benefit", "height", "questionResults", "q", "isCorrect", "question", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizResult.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { \n  TbTrophy, \n  TbCheck, \n  TbX, \n  TbClock, \n  TbBrain, \n  TbArrowRight,\n  TbStar,\n  TbUsers,\n  TbBook,\n  TbMessageCircle,\n  TbChartBar\n} from \"react-icons/tb\";\n\nconst TrialQuizResult = ({ result, onTryAnother, onRegister }) => {\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  const getPerformanceMessage = (percentage) => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n\n  const premiumFeatures = [\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Access comprehensive study materials, notes, and resources\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\"\n    },\n    {\n      icon: TbChartBar,\n      title: \"Ranking System\",\n      description: \"Compete with other students and track your progress\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\"\n    },\n    {\n      icon: TbUsers,\n      title: \"Unlimited Quizzes\",\n      description: \"Take as many quizzes as you want across all subjects\"\n    },\n    {\n      icon: TbStar,\n      title: \"Progress Tracking\",\n      description: \"Detailed analytics and performance insights\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-6 sm:py-12 px-4\">\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Animated Result Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n          className=\"text-center mb-8 sm:mb-12\"\n        >\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ duration: 0.6, delay: 0.3, type: \"spring\", stiffness: 200 }}\n            className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`}\n            onAnimationComplete={() => setAnimationComplete(true)}\n          >\n            <TbTrophy className=\"w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white\" />\n          </motion.div>\n\n          <motion.h1\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\"\n          >\n            Quiz Complete! 🎉\n          </motion.h1>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.8 }}\n            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}\n          >\n            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>\n              {performance.message}\n            </p>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated Score Card */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6, delay: 1.0 }}\n          className=\"bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12\"\n        >\n          {/* Score Header */}\n          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>\n            <div className=\"text-center\">\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ duration: 0.8, delay: 1.2, type: \"spring\" }}\n                className=\"text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2\"\n              >\n                {result.percentage}%\n              </motion.div>\n              <div className=\"text-white/90 text-lg sm:text-xl\">\n                Your Score\n              </div>\n            </div>\n          </div>\n\n          {/* Score Details */}\n          <div className=\"px-6 sm:px-8 lg:px-10 py-8 sm:py-10\"\n        >\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8\">\n              {[\n                {\n                  label: \"Total Questions\",\n                  value: result.totalQuestions,\n                  icon: TbBook,\n                  color: \"blue\",\n                  delay: 1.4\n                },\n                {\n                  label: \"Correct Answers\",\n                  value: result.correctAnswers,\n                  icon: TbCheck,\n                  color: \"green\",\n                  delay: 1.6\n                },\n                {\n                  label: \"Wrong Answers\",\n                  value: result.wrongAnswers,\n                  icon: TbX,\n                  color: \"red\",\n                  delay: 1.8\n                },\n                {\n                  label: \"Time Taken\",\n                  value: formatTime(result.timeSpent),\n                  icon: TbClock,\n                  color: \"purple\",\n                  delay: 2.0\n                }\n              ].map((stat, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: stat.delay }}\n                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}\n                >\n                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>\n                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />\n                  </div>\n                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>\n                    {stat.value}\n                  </div>\n                  <div className=\"text-sm text-gray-600 font-medium\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Pass/Fail Status */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 2.2 }}\n              className=\"text-center mb-8\"\n            >\n              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${\n                isPassed\n                  ? 'bg-green-100 text-green-700 border-2 border-green-200'\n                  : 'bg-red-100 text-red-700 border-2 border-red-200'\n              }`}>\n                {isPassed ? (\n                  <TbCheck className=\"w-6 h-6\" />\n                ) : (\n                  <TbX className=\"w-6 h-6\" />\n                )}\n                <span>\n                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Show Details Button */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 2.4 }}\n              className=\"text-center\"\n            >\n              <button\n                onClick={() => setShowDetails(!showDetails)}\n                className=\"inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200\"\n              >\n                <TbChartBar className=\"w-5 h-5\" />\n                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>\n              </button>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Register Now Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2.6 }}\n          className=\"text-center mb-8\"\n        >\n          <Link to=\"/register\">\n            <motion.button\n              className=\"px-12 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl font-bold text-xl hover:from-blue-700 hover:to-blue-800 transition-all shadow-xl hover:shadow-2xl flex items-center justify-center space-x-3 mx-auto\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <span>Register Now</span>\n              <TbArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n          </Link>\n        </motion.div>\n\n        {/* Unlock Features Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 2.8 }}\n          className=\"bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden mb-8\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-6 sm:px-8 py-6 text-white text-center\">\n            <h3 className=\"text-2xl sm:text-3xl font-bold mb-2\">\n              🔓 Unlock These Amazing Features\n            </h3>\n            <p className=\"text-blue-100 text-lg\">\n              Join thousands of students already excelling with BrainWave\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"p-6 sm:p-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: 3.0 + (0.1 * index) }}\n                  className=\"group bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100 hover:shadow-lg transition-all duration-300\"\n                >\n                  <div className=\"flex items-center space-x-4 mb-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                      <feature.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <h4 className=\"text-lg font-bold text-gray-800\">{feature.title}</h4>\n                  </div>\n                  <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                  <div className=\"mt-4 flex items-center text-green-600 font-medium\">\n                    <TbCheck className=\"w-5 h-5 mr-2\" />\n                    <span className=\"text-sm\">Included in Free Account</span>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Additional Benefits */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 3.6 }}\n              className=\"mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200\"\n            >\n              <h4 className=\"text-xl font-bold text-gray-800 mb-4 text-center\">\n                🎯 Why Students Choose BrainWave\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                {[\n                  { icon: \"🚀\", title: \"Instant Access\", desc: \"Start learning immediately\" },\n                  { icon: \"📱\", title: \"Mobile Friendly\", desc: \"Study anywhere, anytime\" },\n                  { icon: \"🎓\", title: \"Expert Content\", desc: \"Created by top educators\" },\n                  { icon: \"🏆\", title: \"Proven Results\", desc: \"98% success rate\" }\n                ].map((benefit, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.4, delay: 3.8 + (0.1 * index) }}\n                    className=\"text-center p-4 bg-white rounded-xl shadow-sm\"\n                  >\n                    <div className=\"text-2xl mb-2\">{benefit.icon}</div>\n                    <div className=\"font-semibold text-gray-800 mb-1\">{benefit.title}</div>\n                    <div className=\"text-sm text-gray-600\">{benefit.desc}</div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Call to Action */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 4.2 }}\n              className=\"text-center mt-8 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl text-white\"\n            >\n              <h4 className=\"text-xl font-bold mb-2\">Ready to Excel? 🌟</h4>\n              <p className=\"text-blue-100 mb-4\">Join BrainWave today and unlock your full potential!</p>\n              <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\n                <Link to=\"/register\">\n                  <motion.button\n                    className=\"px-8 py-3 bg-white text-blue-600 rounded-xl font-bold hover:bg-blue-50 transition-all shadow-lg flex items-center space-x-2\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span>Create Free Account</span>\n                    <TbArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n                </Link>\n                <div className=\"text-blue-200 text-sm\">\n                  ✨ No credit card required • Start immediately\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Question Details */}\n        {showDetails && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl p-6 mb-8\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-4\">Question Review</h3>\n            <div className=\"space-y-4\">\n              {result.questionResults?.map((q, index) => (\n                <div key={index} className={`p-4 rounded-lg border-2 ${\n                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                }`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\n                    }`}>\n                      {q.isCorrect ? <TbCheck className=\"w-4 h-4\" /> : <TbX className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-gray-800 mb-2\">{q.question}</p>\n                      <div className=\"text-sm space-y-1\">\n                        <p>\n                          <span className=\"text-gray-600\">Your answer:</span>\n                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>\n                            {q.userAnswer || 'Not answered'}\n                          </span>\n                        </p>\n                        {!q.isCorrect && (\n                          <p>\n                            <span className=\"text-gray-600\">Correct answer:</span>\n                            <span className=\"ml-2 font-medium text-green-600\">{q.correctAnswer}</span>\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n\n\n        {/* Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={onTryAnother}\n            className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n          >\n            Try Another Quiz\n          </button>\n          \n          <Link to=\"/\">\n            <button className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\">\n              Back to Home\n            </button>\n          </Link>\n        </motion.div>\n\n        {/* Trial Badge */}\n        <div className=\"text-center mt-8\">\n          <span className=\"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\">\n            🎯 Trial Mode - Register for unlimited access\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,eAAe,EACfC,UAAU,QACL,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM0B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,KAAIG,gBAAiB,GAAE;EAC3C,CAAC;EAED,MAAMC,qBAAqB,GAAIC,UAAU,IAAK;IAC5C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,6BAA6B;MACtCC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,oBAAoB;MAC7BC,KAAK,EAAE,gBAAgB;MACvBC,EAAE,EAAE,aAAa;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,eAAe;MACxBC,KAAK,EAAE,eAAe;MACtBC,EAAE,EAAE,YAAY;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,cAAc;MACvBC,KAAK,EAAE,kBAAkB;MACzBC,EAAE,EAAE,eAAe;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,iBAAiB;MAC1BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO;MACLH,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAGN,qBAAqB,CAACf,MAAM,CAACgB,UAAU,CAAC;EAC5D,MAAMM,QAAQ,GAAGtB,MAAM,CAACgB,UAAU,IAAI,EAAE;EAExC,MAAMO,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE9B,MAAM;IACZ+B,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAElC,OAAO;IACbmC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE5B,UAAU;IAChB6B,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE7B,eAAe;IACrB8B,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE/B,OAAO;IACbgC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhC,MAAM;IACZiC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE5B,OAAA;IAAK6B,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClG9B,OAAA;MAAK6B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhC9B,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,IAAI,EAAE;QAAU,CAAE;QAC/CT,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAErC9B,OAAA,CAACd,MAAM,CAAC6C,GAAG;UACTC,OAAO,EAAE;YAAEO,KAAK,EAAE;UAAE,CAAE;UACtBJ,OAAO,EAAE;YAAEI,KAAK,EAAE;UAAE,CAAE;UACtBH,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE,GAAG;YAAEC,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC1Eb,SAAS,EAAG,mHAAkHN,WAAW,CAACD,QAAS,iBAAiB;UACpKqB,mBAAmB,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,IAAI,CAAE;UAAAoB,QAAA,eAEtD9B,OAAA,CAACZ,QAAQ;YAACyC,SAAS,EAAC;UAAsD;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAEb/C,OAAA,CAACd,MAAM,CAAC8D,EAAE;UACRhB,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CX,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAC1E;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZ/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CX,SAAS,EAAG,uCAAsCN,WAAW,CAACF,EAAG,oBAAmBE,WAAW,CAACH,KAAK,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,MAAM;UAAAnB,QAAA,eAE1H9B,OAAA;YAAG6B,SAAS,EAAG,iCAAgCN,WAAW,CAACH,KAAM,EAAE;YAAAU,QAAA,EAChEP,WAAW,CAACJ;UAAO;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGb/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEM,KAAK,EAAE;QAAI,CAAE;QACpCJ,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEM,KAAK,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CX,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBAGhG9B,OAAA;UAAK6B,SAAS,EAAG,oBAAmBN,WAAW,CAACD,QAAS,qCAAqC;UAAAQ,QAAA,eAC5F9B,OAAA;YAAK6B,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9B,OAAA,CAACd,MAAM,CAAC6C,GAAG;cACTC,OAAO,EAAE;gBAAEO,KAAK,EAAE;cAAE,CAAE;cACtBJ,OAAO,EAAE;gBAAEI,KAAK,EAAE;cAAE,CAAE;cACtBH,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAS,CAAE;cAC1DZ,SAAS,EAAC,4DAA4D;cAAAC,QAAA,GAErE5B,MAAM,CAACgB,UAAU,EAAC,GACrB;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/C,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAElD;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/C,OAAA;UAAK6B,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAElD9B,OAAA;YAAK6B,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EACjE,CACC;cACEoB,KAAK,EAAE,iBAAiB;cACxBC,KAAK,EAAEjD,MAAM,CAACkD,cAAc;cAC5B1B,IAAI,EAAE9B,MAAM;cACZwB,KAAK,EAAE,MAAM;cACboB,KAAK,EAAE;YACT,CAAC,EACD;cACEU,KAAK,EAAE,iBAAiB;cACxBC,KAAK,EAAEjD,MAAM,CAACmD,cAAc;cAC5B3B,IAAI,EAAErC,OAAO;cACb+B,KAAK,EAAE,OAAO;cACdoB,KAAK,EAAE;YACT,CAAC,EACD;cACEU,KAAK,EAAE,eAAe;cACtBC,KAAK,EAAEjD,MAAM,CAACoD,YAAY;cAC1B5B,IAAI,EAAEpC,GAAG;cACT8B,KAAK,EAAE,KAAK;cACZoB,KAAK,EAAE;YACT,CAAC,EACD;cACEU,KAAK,EAAE,YAAY;cACnBC,KAAK,EAAExC,UAAU,CAACT,MAAM,CAACqD,SAAS,CAAC;cACnC7B,IAAI,EAAEnC,OAAO;cACb6B,KAAK,EAAE,QAAQ;cACfoB,KAAK,EAAE;YACT,CAAC,CACF,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChB1D,OAAA,CAACd,MAAM,CAAC6C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAEiB,IAAI,CAACjB;cAAM,CAAE;cACjDX,SAAS,EAAG,iBAAgB4B,IAAI,CAACrC,KAAM,iCAAgCqC,IAAI,CAACrC,KAAM,kBAAkB;cAAAU,QAAA,gBAEpG9B,OAAA;gBAAK6B,SAAS,EAAG,6BAA4B4B,IAAI,CAACrC,KAAM,kDAAkD;gBAAAU,QAAA,eACxG9B,OAAA,CAACyD,IAAI,CAAC/B,IAAI;kBAACG,SAAS,EAAG,gBAAe4B,IAAI,CAACrC,KAAM;gBAAM;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN/C,OAAA;gBAAK6B,SAAS,EAAG,uCAAsC4B,IAAI,CAACrC,KAAM,WAAW;gBAAAU,QAAA,EAC1E2B,IAAI,CAACN;cAAK;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACN/C,OAAA;gBAAK6B,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/C2B,IAAI,CAACP;cAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GAdDW,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEM,KAAK,EAAE;YAAI,CAAE;YACpCJ,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEM,KAAK,EAAE;YAAE,CAAE;YAClCH,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CX,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAE5B9B,OAAA;cAAK6B,SAAS,EAAG,kFACfL,QAAQ,GACJ,uDAAuD,GACvD,iDACL,EAAE;cAAAM,QAAA,GACAN,QAAQ,gBACPxB,OAAA,CAACX,OAAO;gBAACwC,SAAS,EAAC;cAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE/B/C,OAAA,CAACV,GAAG;gBAACuC,SAAS,EAAC;cAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAC3B,eACD/C,OAAA;gBAAA8B,QAAA,EACGN,QAAQ,GAAG,iCAAiC,GAAG;cAAsC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGb/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBG,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CX,SAAS,EAAC,aAAa;YAAAC,QAAA,eAEvB9B,OAAA;cACE2D,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC,CAACD,WAAW,CAAE;cAC5CsB,SAAS,EAAC,6IAA6I;cAAAC,QAAA,gBAEvJ9B,OAAA,CAACF,UAAU;gBAAC+B,SAAS,EAAC;cAAS;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClC/C,OAAA;gBAAA8B,QAAA,EAAOvB,WAAW,GAAG,uBAAuB,GAAG;cAAuB;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CX,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAE5B9B,OAAA,CAACb,IAAI;UAACyE,EAAE,EAAC,WAAW;UAAA9B,QAAA,eAClB9B,OAAA,CAACd,MAAM,CAAC2E,MAAM;YACZhC,SAAS,EAAC,mOAAmO;YAC7OiC,UAAU,EAAE;cAAEvB,KAAK,EAAE,IAAI;cAAEL,CAAC,EAAE,CAAC;YAAE,CAAE;YACnC6B,QAAQ,EAAE;cAAExB,KAAK,EAAE;YAAK,CAAE;YAAAT,QAAA,gBAE1B9B,OAAA;cAAA8B,QAAA,EAAM;YAAY;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzB/C,OAAA,CAACP,YAAY;cAACoC,SAAS,EAAC;YAAS;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGb/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CX,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBAGtF9B,OAAA;UAAK6B,SAAS,EAAC,uFAAuF;UAAAC,QAAA,gBACpG9B,OAAA;YAAI6B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL/C,OAAA;YAAG6B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN/C,OAAA;UAAK6B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9B,OAAA;YAAK6B,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAClEL,eAAe,CAAC+B,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBAClC1D,OAAA,CAACd,MAAM,CAAC6C,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGkB;cAAO,CAAE;cAC1D7B,SAAS,EAAC,sIAAsI;cAAAC,QAAA,gBAEhJ9B,OAAA;gBAAK6B,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C9B,OAAA;kBAAK6B,SAAS,EAAC,4JAA4J;kBAAAC,QAAA,eACzK9B,OAAA,CAACgE,OAAO,CAACtC,IAAI;oBAACG,SAAS,EAAC;kBAAoB;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACN/C,OAAA;kBAAI6B,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAEkC,OAAO,CAACrC;gBAAK;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN/C,OAAA;gBAAG6B,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAEkC,OAAO,CAACpC;cAAW;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtE/C,OAAA;gBAAK6B,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChE9B,OAAA,CAACX,OAAO;kBAACwC,SAAS,EAAC;gBAAc;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpC/C,OAAA;kBAAM6B,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA,GAhBDW,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CX,SAAS,EAAC,wFAAwF;YAAAC,QAAA,gBAElG9B,OAAA;cAAI6B,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAEjE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/C,OAAA;cAAK6B,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAClE,CACC;gBAAEJ,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAEsC,IAAI,EAAE;cAA6B,CAAC,EAC3E;gBAAEvC,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,iBAAiB;gBAAEsC,IAAI,EAAE;cAA0B,CAAC,EACzE;gBAAEvC,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAEsC,IAAI,EAAE;cAA2B,CAAC,EACzE;gBAAEvC,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE,gBAAgB;gBAAEsC,IAAI,EAAE;cAAmB,CAAC,CAClE,CAACT,GAAG,CAAC,CAACU,OAAO,EAAER,KAAK,kBACnB1D,OAAA,CAACd,MAAM,CAAC6C,GAAG;gBAETC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAI,CAAE;gBACpCJ,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAE,CAAE;gBAClCH,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEG,KAAK,EAAE,GAAG,GAAI,GAAG,GAAGkB;gBAAO,CAAE;gBAC1D7B,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAEzD9B,OAAA;kBAAK6B,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEoC,OAAO,CAACxC;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnD/C,OAAA;kBAAK6B,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEoC,OAAO,CAACvC;gBAAK;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvE/C,OAAA;kBAAK6B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEoC,OAAO,CAACD;gBAAI;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GARtDW,KAAK;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGb/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEG,KAAK,EAAE;YAAI,CAAE;YAC1CX,SAAS,EAAC,0FAA0F;YAAAC,QAAA,gBAEpG9B,OAAA;cAAI6B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAkB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9D/C,OAAA;cAAG6B,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAoD;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1F/C,OAAA;cAAK6B,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC1E9B,OAAA,CAACb,IAAI;gBAACyE,EAAE,EAAC,WAAW;gBAAA9B,QAAA,eAClB9B,OAAA,CAACd,MAAM,CAAC2E,MAAM;kBACZhC,SAAS,EAAC,6HAA6H;kBACvIiC,UAAU,EAAE;oBAAEvB,KAAK,EAAE;kBAAK,CAAE;kBAC5BwB,QAAQ,EAAE;oBAAExB,KAAK,EAAE;kBAAK,CAAE;kBAAAT,QAAA,gBAE1B9B,OAAA;oBAAA8B,QAAA,EAAM;kBAAmB;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChC/C,OAAA,CAACP,YAAY;oBAACoC,SAAS,EAAC;kBAAS;oBAAAe,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACP/C,OAAA;gBAAK6B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZxC,WAAW,iBACVP,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEkC,MAAM,EAAE;QAAE,CAAE;QACnChC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEkC,MAAM,EAAE;QAAO,CAAE;QACxC/B,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEnD9B,OAAA;UAAI6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAe;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE/C,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAAxB,qBAAA,GACvBJ,MAAM,CAACkE,eAAe,cAAA9D,qBAAA,uBAAtBA,qBAAA,CAAwBkD,GAAG,CAAC,CAACa,CAAC,EAAEX,KAAK,kBACpC1D,OAAA;YAAiB6B,SAAS,EAAG,2BAC3BwC,CAAC,CAACC,SAAS,GAAG,8BAA8B,GAAG,0BAChD,EAAE;YAAAxC,QAAA,eACD9B,OAAA;cAAK6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC9B,OAAA;gBAAK6B,SAAS,EAAG,yDACfwC,CAAC,CAACC,SAAS,GAAG,yBAAyB,GAAG,uBAC3C,EAAE;gBAAAxC,QAAA,EACAuC,CAAC,CAACC,SAAS,gBAAGtE,OAAA,CAACX,OAAO;kBAACwC,SAAS,EAAC;gBAAS;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG/C,OAAA,CAACV,GAAG;kBAACuC,SAAS,EAAC;gBAAS;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACN/C,OAAA;gBAAK6B,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB9B,OAAA;kBAAG6B,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAEuC,CAAC,CAACE;gBAAQ;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9D/C,OAAA;kBAAK6B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9B,OAAA;oBAAA8B,QAAA,gBACE9B,OAAA;sBAAM6B,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAY;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnD/C,OAAA;sBAAM6B,SAAS,EAAG,oBAAmBwC,CAAC,CAACC,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;sBAAAxC,QAAA,EACpFuC,CAAC,CAACG,UAAU,IAAI;oBAAc;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACH,CAACsB,CAAC,CAACC,SAAS,iBACXtE,OAAA;oBAAA8B,QAAA,gBACE9B,OAAA;sBAAM6B,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAe;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtD/C,OAAA;sBAAM6B,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEuC,CAAC,CAACI;oBAAa;sBAAA7B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA1BEW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAKD/C,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEG,KAAK,EAAE;QAAI,CAAE;QAC1CX,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE1D9B,OAAA;UACE2D,OAAO,EAAExD,YAAa;UACtB0B,SAAS,EAAC,0GAA0G;UAAAC,QAAA,EACrH;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET/C,OAAA,CAACb,IAAI;UAACyE,EAAE,EAAC,GAAG;UAAA9B,QAAA,eACV9B,OAAA;YAAQ6B,SAAS,EAAC,6FAA6F;YAAAC,QAAA,EAAC;UAEhH;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGb/C,OAAA;QAAK6B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B9B,OAAA;UAAM6B,SAAS,EAAC,uFAAuF;UAAAC,QAAA,EAAC;QAExG;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA3bIJ,eAAe;AAAAyE,EAAA,GAAfzE,eAAe;AA6brB,eAAeA,eAAe;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}