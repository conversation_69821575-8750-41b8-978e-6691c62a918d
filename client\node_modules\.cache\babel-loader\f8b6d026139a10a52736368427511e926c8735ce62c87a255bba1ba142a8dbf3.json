{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizPlay = ({\n  quizData,\n  onComplete,\n  onBack\n}) => {\n  _s();\n  var _currentQuestion$opti;\n  const {\n    exam,\n    trialUserInfo\n  } = quizData;\n  const questions = exam.questions || [];\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n\n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n  const goToQuestion = index => {\n    setCurrentQuestionIndex(index);\n  };\n  if (questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-800 mb-2\",\n          children: \"No Questions Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz doesn't have any questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onBack,\n              className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-w-0 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-lg sm:text-xl font-bold text-gray-800 truncate\",\n                children: exam.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs sm:text-sm text-gray-600\",\n                children: [\"Trial Mode \\u2022 \", exam.subject]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-2 rounded-lg ${timeLeft <= 60 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-3 h-3 sm:w-4 sm:h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-xs sm:text-sm\",\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm text-gray-600 hidden sm:block\",\n              children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sm:hidden mt-2 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-600\",\n            children: [answeredQuestions, \"/\", questions.length, \" answered\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-b\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 overflow-x-auto pb-2\",\n          children: questions.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => goToQuestion(index),\n            className: `flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-lg font-medium transition-all text-sm sm:text-base ${index === currentQuestionIndex ? 'bg-blue-600 text-white' : selectedAnswers[questions[index]._id] ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n            children: index + 1\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 py-4 sm:py-8\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          x: 20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-lg p-4 sm:p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-blue-600\",\n              children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500\",\n              children: currentQuestion.answerType || 'Multiple Choice'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg sm:text-xl font-semibold text-gray-800 leading-relaxed\",\n            children: currentQuestion.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), currentQuestion.image && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: currentQuestion.image,\n            alt: \"Question\",\n            className: \"max-w-full h-auto rounded-lg shadow-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3 mb-8\",\n          children: (_currentQuestion$opti = currentQuestion.options) === null || _currentQuestion$opti === void 0 ? void 0 : _currentQuestion$opti.map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n            const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: () => handleAnswerSelect(currentQuestion._id, optionLetter),\n              className: `w-full p-4 text-left rounded-xl border-2 transition-all ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'}`,\n              whileHover: {\n                scale: 1.01\n              },\n              whileTap: {\n                scale: 0.99\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-medium ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                  children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 37\n                  }, this) : optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-1\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToPrevious,\n            disabled: currentQuestionIndex === 0,\n            className: `flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all ${currentQuestionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), isLastQuestion ? /*#__PURE__*/_jsxDEV(motion.button, {\n            onClick: handleSubmitQuiz,\n            disabled: isSubmitting,\n            className: `flex items-center space-x-2 px-8 py-3 rounded-lg font-medium transition-all ${isSubmitting ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-green-600 text-white hover:bg-green-700 shadow-lg hover:shadow-xl'}`,\n            whileHover: !isSubmitting ? {\n              scale: 1.02\n            } : {},\n            whileTap: !isSubmitting ? {\n              scale: 0.98\n            } : {},\n            children: [isSubmitting ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: isSubmitting ? 'Submitting...' : 'Submit Quiz'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goToNext,\n            className: \"flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Next\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, currentQuestionIndex, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\",\n      children: \"Trial Mode\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizPlay, \"poqwUhvGWiexGFrQc9xn7WhRFI8=\");\n_c = TrialQuizPlay;\nexport default TrialQuizPlay;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "TbArrowLeft", "TbArrowRight", "TbClock", "TbCheck", "message", "submitTrialResult", "jsxDEV", "_jsxDEV", "TrialQuizPlay", "quizData", "onComplete", "onBack", "_s", "_currentQuestion$opti", "exam", "trialUserInfo", "questions", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedAnswer<PERSON>", "setSelectedAnswers", "timeLeft", "setTimeLeft", "duration", "isSubmitting", "setIsSubmitting", "startTime", "Date", "now", "handleSubmitQuiz", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "questionId", "answer", "timeSpent", "round", "response", "examId", "_id", "answers", "success", "data", "error", "console", "goToNext", "length", "goToPrevious", "goToQuestion", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "isLastQuestion", "answeredQuestions", "Object", "keys", "name", "subject", "map", "_", "div", "initial", "opacity", "x", "animate", "transition", "answerType", "image", "src", "alt", "options", "option", "optionLetter", "String", "fromCharCode", "isSelected", "button", "whileHover", "scale", "whileTap", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowLeft, TbArrowRight, TbClock, TbCheck } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { submitTrialResult } from \"../../apicalls/trial\";\n\nconst TrialQuizPlay = ({ quizData, onComplete, onBack }) => {\n  const { exam, trialUserInfo } = quizData;\n  const questions = exam.questions || [];\n  \n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedAnswers, setSelectedAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState((exam.duration || 10) * 60); // Convert minutes to seconds\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [startTime] = useState(Date.now());\n\n  // Timer effect\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (questionId, answer) => {\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = useCallback(async () => {\n    if (isSubmitting) return;\n\n    setIsSubmitting(true);\n    try {\n      const timeSpent = Math.round((Date.now() - startTime) / 1000); // Time in seconds\n      \n      const response = await submitTrialResult({\n        examId: exam._id,\n        answers: selectedAnswers,\n        timeSpent,\n        trialUserInfo\n      });\n\n      if (response.success) {\n        onComplete(response.data);\n      } else {\n        message.error(response.message || \"Failed to submit quiz\");\n        setIsSubmitting(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Error submitting trial quiz:\", error);\n      message.error(\"Something went wrong. Please try again.\");\n      setIsSubmitting(false);\n    }\n  }, [exam._id, selectedAnswers, trialUserInfo, startTime, onComplete, isSubmitting]);\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n    }\n  };\n\n  const goToQuestion = (index) => {\n    setCurrentQuestionIndex(index);\n  };\n\n  if (questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-2\">No Questions Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions.</p>\n          <button\n            onClick={onBack}\n            className=\"py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Go Back\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const isLastQuestion = currentQuestionIndex === questions.length - 1;\n  const answeredQuestions = Object.keys(selectedAnswers).length;\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1\">\n              <button\n                onClick={onBack}\n                className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors flex-shrink-0\"\n              >\n                <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              </button>\n              <div className=\"min-w-0 flex-1\">\n                <h1 className=\"text-lg sm:text-xl font-bold text-gray-800 truncate\">{exam.name}</h1>\n                <p className=\"text-xs sm:text-sm text-gray-600\">Trial Mode • {exam.subject}</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\n              <div className={`flex items-center space-x-1 sm:space-x-2 px-2 sm:px-3 py-1 sm:py-2 rounded-lg ${\n                timeLeft <= 60 ? 'bg-red-100 text-red-700' : 'bg-blue-100 text-blue-700'\n              }`}>\n                <TbClock className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                <span className=\"font-medium text-xs sm:text-sm\">{formatTime(timeLeft)}</span>\n              </div>\n\n              <div className=\"text-xs sm:text-sm text-gray-600 hidden sm:block\">\n                {answeredQuestions}/{questions.length} answered\n              </div>\n            </div>\n          </div>\n\n          {/* Mobile answered questions indicator */}\n          <div className=\"sm:hidden mt-2 text-center\">\n            <span className=\"text-xs text-gray-600\">\n              {answeredQuestions}/{questions.length} answered\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Question Navigation */}\n      <div className=\"bg-white border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 py-3\">\n          <div className=\"flex items-center space-x-2 overflow-x-auto pb-2\">\n            {questions.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToQuestion(index)}\n                className={`flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-lg font-medium transition-all text-sm sm:text-base ${\n                  index === currentQuestionIndex\n                    ? 'bg-blue-600 text-white'\n                    : selectedAnswers[questions[index]._id]\n                    ? 'bg-green-100 text-green-700 border border-green-300'\n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                }`}\n              >\n                {index + 1}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Question Content */}\n      <div className=\"max-w-4xl mx-auto px-4 py-4 sm:py-8\">\n        <motion.div\n          key={currentQuestionIndex}\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.3 }}\n          className=\"bg-white rounded-2xl shadow-lg p-4 sm:p-8\"\n        >\n          {/* Question Header */}\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <span className=\"text-sm font-medium text-blue-600\">\n                Question {currentQuestionIndex + 1} of {questions.length}\n              </span>\n              <span className=\"text-sm text-gray-500\">\n                {currentQuestion.answerType || 'Multiple Choice'}\n              </span>\n            </div>\n            \n            <h2 className=\"text-lg sm:text-xl font-semibold text-gray-800 leading-relaxed\">\n              {currentQuestion.name}\n            </h2>\n          </div>\n\n          {/* Question Image (if exists) */}\n          {currentQuestion.image && (\n            <div className=\"mb-6\">\n              <img\n                src={currentQuestion.image}\n                alt=\"Question\"\n                className=\"max-w-full h-auto rounded-lg shadow-sm\"\n              />\n            </div>\n          )}\n\n          {/* Answer Options */}\n          <div className=\"space-y-3 mb-8\">\n            {currentQuestion.options?.map((option, index) => {\n              const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n              const isSelected = selectedAnswers[currentQuestion._id] === optionLetter;\n              \n              return (\n                <motion.button\n                  key={index}\n                  onClick={() => handleAnswerSelect(currentQuestion._id, optionLetter)}\n                  className={`w-full p-4 text-left rounded-xl border-2 transition-all ${\n                    isSelected\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'\n                  }`}\n                  whileHover={{ scale: 1.01 }}\n                  whileTap={{ scale: 0.99 }}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-medium ${\n                      isSelected\n                        ? 'border-blue-500 bg-blue-500 text-white'\n                        : 'border-gray-300 text-gray-600'\n                    }`}>\n                      {isSelected ? <TbCheck className=\"w-4 h-4\" /> : optionLetter}\n                    </div>\n                    <span className=\"flex-1\">{option}</span>\n                  </div>\n                </motion.button>\n              );\n            })}\n          </div>\n\n          {/* Navigation Buttons */}\n          <div className=\"flex items-center justify-between\">\n            <button\n              onClick={goToPrevious}\n              disabled={currentQuestionIndex === 0}\n              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all ${\n                currentQuestionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4\" />\n              <span>Previous</span>\n            </button>\n\n            {isLastQuestion ? (\n              <motion.button\n                onClick={handleSubmitQuiz}\n                disabled={isSubmitting}\n                className={`flex items-center space-x-2 px-8 py-3 rounded-lg font-medium transition-all ${\n                  isSubmitting\n                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                    : 'bg-green-600 text-white hover:bg-green-700 shadow-lg hover:shadow-xl'\n                }`}\n                whileHover={!isSubmitting ? { scale: 1.02 } : {}}\n                whileTap={!isSubmitting ? { scale: 0.98 } : {}}\n              >\n                {isSubmitting ? (\n                  <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n                ) : (\n                  <TbCheck className=\"w-4 h-4\" />\n                )}\n                <span>{isSubmitting ? 'Submitting...' : 'Submit Quiz'}</span>\n              </motion.button>\n            ) : (\n              <button\n                onClick={goToNext}\n                className=\"flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all font-medium\"\n              >\n                <span>Next</span>\n                <TbArrowRight className=\"w-4 h-4\" />\n              </button>\n            )}\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Trial Watermark */}\n      <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg\">\n        Trial Mode\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC5E,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,iBAAiB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC;EAAc,CAAC,GAAGN,QAAQ;EACxC,MAAMO,SAAS,GAAGF,IAAI,CAACE,SAAS,IAAI,EAAE;EAEtC,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,CAACkB,IAAI,CAACS,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACtE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8B,SAAS,CAAC,GAAG9B,QAAQ,CAAC+B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;EAExC;EACA/B,SAAS,CAAC,MAAM;IACd,IAAIwB,QAAQ,IAAI,CAAC,EAAE;MACjBQ,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BT,WAAW,CAACU,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMa,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;IACjDxB,kBAAkB,CAACY,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACW,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMf,gBAAgB,GAAG/B,WAAW,CAAC,YAAY;IAC/C,IAAI0B,YAAY,EAAE;IAElBC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMoB,SAAS,GAAGR,IAAI,CAACS,KAAK,CAAC,CAACnB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;;MAE/D,MAAMqB,QAAQ,GAAG,MAAM1C,iBAAiB,CAAC;QACvC2C,MAAM,EAAElC,IAAI,CAACmC,GAAG;QAChBC,OAAO,EAAE/B,eAAe;QACxB0B,SAAS;QACT9B;MACF,CAAC,CAAC;MAEF,IAAIgC,QAAQ,CAACI,OAAO,EAAE;QACpBzC,UAAU,CAACqC,QAAQ,CAACK,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLhD,OAAO,CAACiD,KAAK,CAACN,QAAQ,CAAC3C,OAAO,IAAI,uBAAuB,CAAC;QAC1DqB,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDjD,OAAO,CAACiD,KAAK,CAAC,yCAAyC,CAAC;MACxD5B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACX,IAAI,CAACmC,GAAG,EAAE9B,eAAe,EAAEJ,aAAa,EAAEW,SAAS,EAAEhB,UAAU,EAAEc,YAAY,CAAC,CAAC;;EAEnF;EACA,MAAM+B,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAItC,oBAAoB,GAAGD,SAAS,CAACwC,MAAM,GAAG,CAAC,EAAE;MAC/CtC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIxC,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;IACnD;EACF,CAAC;EAED,MAAMyC,YAAY,GAAIC,KAAK,IAAK;IAC9BzC,uBAAuB,CAACyC,KAAK,CAAC;EAChC,CAAC;EAED,IAAI3C,SAAS,CAACwC,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACEjD,OAAA;MAAKqD,SAAS,EAAC,uGAAuG;MAAAC,QAAA,eACpHtD,OAAA;QAAKqD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtD,OAAA;UAAIqD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF1D,OAAA;UAAGqD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3E1D,OAAA;UACE2D,OAAO,EAAEvD,MAAO;UAChBiD,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAGnD,SAAS,CAACC,oBAAoB,CAAC;EACvD,MAAMmD,cAAc,GAAGnD,oBAAoB,KAAKD,SAAS,CAACwC,MAAM,GAAG,CAAC;EACpE,MAAMa,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAACpD,eAAe,CAAC,CAACqC,MAAM;EAE7D,oBACEjD,OAAA;IAAKqD,SAAS,EAAC,kEAAkE;IAAAC,QAAA,gBAE/EtD,OAAA;MAAKqD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CtD,OAAA;QAAKqD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CtD,OAAA;UAAKqD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDtD,OAAA;YAAKqD,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtEtD,OAAA;cACE2D,OAAO,EAAEvD,MAAO;cAChBiD,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAE5EtD,OAAA,CAACP,WAAW;gBAAC4D,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACT1D,OAAA;cAAKqD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BtD,OAAA;gBAAIqD,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAE/C,IAAI,CAAC0D;cAAI;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpF1D,OAAA;gBAAGqD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,GAAC,oBAAa,EAAC/C,IAAI,CAAC2D,OAAO;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1D,OAAA;YAAKqD,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBACrEtD,OAAA;cAAKqD,SAAS,EAAG,iFACfvC,QAAQ,IAAI,EAAE,GAAG,yBAAyB,GAAG,2BAC9C,EAAE;cAAAwC,QAAA,gBACDtD,OAAA,CAACL,OAAO;gBAAC0D,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7C1D,OAAA;gBAAMqD,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAE3B,UAAU,CAACb,QAAQ;cAAC;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEN1D,OAAA;cAAKqD,SAAS,EAAC,kDAAkD;cAAAC,QAAA,GAC9DQ,iBAAiB,EAAC,GAAC,EAACrD,SAAS,CAACwC,MAAM,EAAC,WACxC;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1D,OAAA;UAAKqD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzCtD,OAAA;YAAMqD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpCQ,iBAAiB,EAAC,GAAC,EAACrD,SAAS,CAACwC,MAAM,EAAC,WACxC;UAAA;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1D,OAAA;MAAKqD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCtD,OAAA;QAAKqD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CtD,OAAA;UAAKqD,SAAS,EAAC,kDAAkD;UAAAC,QAAA,EAC9D7C,SAAS,CAAC0D,GAAG,CAAC,CAACC,CAAC,EAAEhB,KAAK,kBACtBpD,OAAA;YAEE2D,OAAO,EAAEA,CAAA,KAAMR,YAAY,CAACC,KAAK,CAAE;YACnCC,SAAS,EAAG,oGACVD,KAAK,KAAK1C,oBAAoB,GAC1B,wBAAwB,GACxBE,eAAe,CAACH,SAAS,CAAC2C,KAAK,CAAC,CAACV,GAAG,CAAC,GACrC,qDAAqD,GACrD,6CACL,EAAE;YAAAY,QAAA,EAEFF,KAAK,GAAG;UAAC,GAVLA,KAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1D,OAAA;MAAKqD,SAAS,EAAC,qCAAqC;MAAAC,QAAA,eAClDtD,OAAA,CAACR,MAAM,CAAC6E,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE1D,QAAQ,EAAE;QAAI,CAAE;QAC9BqC,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBAGrDtD,OAAA;UAAKqD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBtD,OAAA;YAAKqD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDtD,OAAA;cAAMqD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAAC,WACzC,EAAC5C,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACD,SAAS,CAACwC,MAAM;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACP1D,OAAA;cAAMqD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACpCM,eAAe,CAACe,UAAU,IAAI;YAAiB;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN1D,OAAA;YAAIqD,SAAS,EAAC,gEAAgE;YAAAC,QAAA,EAC3EM,eAAe,CAACK;UAAI;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAGLE,eAAe,CAACgB,KAAK,iBACpB5E,OAAA;UAAKqD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBtD,OAAA;YACE6E,GAAG,EAAEjB,eAAe,CAACgB,KAAM;YAC3BE,GAAG,EAAC,UAAU;YACdzB,SAAS,EAAC;UAAwC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD1D,OAAA;UAAKqD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAAAhD,qBAAA,GAC5BsD,eAAe,CAACmB,OAAO,cAAAzE,qBAAA,uBAAvBA,qBAAA,CAAyB6D,GAAG,CAAC,CAACa,MAAM,EAAE5B,KAAK,KAAK;YAC/C,MAAM6B,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG/B,KAAK,CAAC,CAAC,CAAC;YACtD,MAAMgC,UAAU,GAAGxE,eAAe,CAACgD,eAAe,CAAClB,GAAG,CAAC,KAAKuC,YAAY;YAExE,oBACEjF,OAAA,CAACR,MAAM,CAAC6F,MAAM;cAEZ1B,OAAO,EAAEA,CAAA,KAAMxB,kBAAkB,CAACyB,eAAe,CAAClB,GAAG,EAAEuC,YAAY,CAAE;cACrE5B,SAAS,EAAG,2DACV+B,UAAU,GACN,0CAA0C,GAC1C,wDACL,EAAE;cACHE,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAjC,QAAA,eAE1BtD,OAAA;gBAAKqD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CtD,OAAA;kBAAKqD,SAAS,EAAG,8EACf+B,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;kBAAA9B,QAAA,EACA8B,UAAU,gBAAGpF,OAAA,CAACJ,OAAO;oBAACyD,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GAAGuB;gBAAY;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACN1D,OAAA;kBAAMqD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAAE0B;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC,GAnBDN,KAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBG,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1D,OAAA;UAAKqD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDtD,OAAA;YACE2D,OAAO,EAAET,YAAa;YACtBuC,QAAQ,EAAE/E,oBAAoB,KAAK,CAAE;YACrC2C,SAAS,EAAG,+EACV3C,oBAAoB,KAAK,CAAC,GACtB,8CAA8C,GAC9C,6CACL,EAAE;YAAA4C,QAAA,gBAEHtD,OAAA,CAACP,WAAW;cAAC4D,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC1D,OAAA;cAAAsD,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EAERG,cAAc,gBACb7D,OAAA,CAACR,MAAM,CAAC6F,MAAM;YACZ1B,OAAO,EAAErC,gBAAiB;YAC1BmE,QAAQ,EAAExE,YAAa;YACvBoC,SAAS,EAAG,+EACVpC,YAAY,GACR,8CAA8C,GAC9C,sEACL,EAAE;YACHqE,UAAU,EAAE,CAACrE,YAAY,GAAG;cAAEsE,KAAK,EAAE;YAAK,CAAC,GAAG,CAAC,CAAE;YACjDC,QAAQ,EAAE,CAACvE,YAAY,GAAG;cAAEsE,KAAK,EAAE;YAAK,CAAC,GAAG,CAAC,CAAE;YAAAjC,QAAA,GAE9CrC,YAAY,gBACXjB,OAAA;cAAKqD,SAAS,EAAC;YAA2E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE7F1D,OAAA,CAACJ,OAAO;cAACyD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAC/B,eACD1D,OAAA;cAAAsD,QAAA,EAAOrC,YAAY,GAAG,eAAe,GAAG;YAAa;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,gBAEhB1D,OAAA;YACE2D,OAAO,EAAEX,QAAS;YAClBK,SAAS,EAAC,sHAAsH;YAAAC,QAAA,gBAEhItD,OAAA;cAAAsD,QAAA,EAAM;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjB1D,OAAA,CAACN,YAAY;cAAC2D,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GA7GDhD,oBAAoB;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8Gf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN1D,OAAA;MAAKqD,SAAS,EAAC,oGAAoG;MAAAC,QAAA,EAAC;IAEpH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CApSIJ,aAAa;AAAAyF,EAAA,GAAbzF,aAAa;AAsSnB,eAAeA,aAAa;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}