{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\TryForFreeModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbX, TbBrain, TbSchool, TbUser, TbArrowRight } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TryForFreeModal = ({\n  isOpen,\n  onClose,\n  onSubmit\n}) => {\n  _s();\n  var _classOptions$formDat;\n  const [formData, setFormData] = useState({\n    name: \"\",\n    level: \"\",\n    class: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n\n  // Level and class configurations\n  const levelOptions = [{\n    value: \"primary\",\n    label: \"Primary\",\n    description: \"Classes 1-7\",\n    icon: \"🌱\"\n  }, {\n    value: \"secondary\",\n    label: \"Secondary\",\n    description: \"Form 1-4\",\n    icon: \"📚\"\n  }, {\n    value: \"advance\",\n    label: \"Advance\",\n    description: \"Form 5-6\",\n    icon: \"🎓\"\n  }];\n  const classOptions = {\n    primary: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"],\n    secondary: [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"],\n    advance: [\"Form-5\", \"Form-6\"]\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n      // Reset class when level changes\n      ...(field === \"level\" && {\n        class: \"\"\n      })\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.name.trim()) {\n      message.error(\"Please enter your name\");\n      return;\n    }\n    if (!formData.level) {\n      message.error(\"Please select your level\");\n      return;\n    }\n    if (!formData.class) {\n      message.error(\"Please select your class\");\n      return;\n    }\n    setLoading(true);\n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      message.error(\"Something went wrong. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const modalVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 0.8,\n      y: 50\n    },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      y: 0,\n      transition: {\n        type: \"spring\",\n        damping: 25,\n        stiffness: 300\n      }\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.8,\n      y: 50,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n  const overlayVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    }\n  };\n  console.log(\"TryForFreeModal render check - isOpen:\", isOpen);\n  if (!isOpen) {\n    console.log(\"Modal not rendering because isOpen is false\");\n    return null;\n  }\n  console.log(\"Modal should be rendering now!\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50\",\n    style: {\n      zIndex: 9999,\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0,0,0,0.5)'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl\",\n      style: {\n        backgroundColor: 'white',\n        padding: '24px',\n        borderRadius: '8px',\n        maxWidth: '400px',\n        width: '90%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold\",\n          style: {\n            color: 'black',\n            fontSize: '24px'\n          },\n          children: \"\\uD83C\\uDF89 MODAL IS WORKING! \\uD83C\\uDF89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"text-gray-500 hover:text-gray-700\",\n          style: {\n            fontSize: '20px',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-4\",\n        style: {\n          color: 'black'\n        },\n        children: \"If you can see this, the modal is working! Experience our platform with a sample quiz.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Your Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: formData.name,\n            onChange: e => handleInputChange(\"name\", e.target.value),\n            className: \"w-full px-3 py-2 border rounded-lg\",\n            placeholder: \"Enter your name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.level,\n            onChange: e => handleInputChange(\"level\", e.target.value),\n            className: \"w-full px-3 py-2 border rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"primary\",\n              children: \"Primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"secondary\",\n              children: \"Secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"advance\",\n              children: \"Advance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium mb-2\",\n            children: \"Class\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.class,\n            onChange: e => handleInputChange(\"class\", e.target.value),\n            className: \"w-full px-3 py-2 border rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select Class\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), formData.level === \"primary\" && [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"].map(c => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: c,\n              children: [\"Class \", c]\n            }, c, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)), formData.level === \"secondary\" && [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"].map(c => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: c,\n              children: c\n            }, c, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)), formData.level === \"advance\" && [\"Form-5\", \"Form-6\"].map(c => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: c,\n              children: c\n            }, c, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSubmit,\n          disabled: loading,\n          className: \"w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\",\n          children: loading ? \"Loading...\" : \"Start Free Trial\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-4 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 sm:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-2 bg-white/20 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-5 h-5 sm:w-6 sm:h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-lg sm:text-xl font-bold\",\n                  children: \"Try BrainWave Free\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-100 text-xs sm:text-sm\",\n                  children: \"Experience our platform with a sample quiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"p-2 hover:bg-white/20 rounded-lg transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"p-4 sm:p-6 space-y-4 sm:space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center space-x-2 text-sm font-medium text-gray-700\",\n              children: [/*#__PURE__*/_jsxDEV(TbUser, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Your Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: formData.name,\n              onChange: e => handleInputChange(\"name\", e.target.value),\n              placeholder: \"Enter your full name\",\n              className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n              maxLength: 50\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center space-x-2 text-sm font-medium text-gray-700\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Education Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid gap-3\",\n              children: levelOptions.map(level => /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => handleInputChange(\"level\", level.value),\n                className: `p-4 border-2 rounded-lg text-left transition-all ${formData.level === level.value ? \"border-blue-500 bg-blue-50 text-blue-700\" : \"border-gray-200 hover:border-blue-300 hover:bg-blue-50\"}`,\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl\",\n                    children: level.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium\",\n                      children: level.label\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: level.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this)\n              }, level.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), formData.level && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"space-y-3\",\n            initial: {\n              opacity: 0,\n              height: 0\n            },\n            animate: {\n              opacity: 1,\n              height: \"auto\"\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Select Your Class\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 sm:grid-cols-3 gap-2\",\n              children: (_classOptions$formDat = classOptions[formData.level]) === null || _classOptions$formDat === void 0 ? void 0 : _classOptions$formDat.map(classOption => /*#__PURE__*/_jsxDEV(motion.button, {\n                type: \"button\",\n                onClick: () => handleInputChange(\"class\", classOption),\n                className: `p-2 sm:p-3 border-2 rounded-lg text-center font-medium transition-all text-sm sm:text-base ${formData.class === classOption ? \"border-blue-500 bg-blue-500 text-white\" : \"border-gray-200 hover:border-blue-300 hover:bg-blue-50\"}`,\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: classOption\n              }, classOption, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"submit\",\n            disabled: loading || !formData.name || !formData.level || !formData.class,\n            className: `w-full py-4 px-6 rounded-lg font-medium flex items-center justify-center space-x-2 transition-all ${loading || !formData.name || !formData.level || !formData.class ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl\"}`,\n            whileHover: !loading && formData.name && formData.level && formData.class ? {\n              scale: 1.02\n            } : {},\n            whileTap: !loading && formData.name && formData.level && formData.class ? {\n              scale: 0.98\n            } : {},\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Start Free Trial\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No registration required for trial\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1\",\n              children: \"Experience one quiz to see what BrainWave offers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n_s(TryForFreeModal, \"QhkLpP7u0NRpTxVhR7Y0TXXfUKY=\");\n_c = TryForFreeModal;\nexport default TryForFreeModal;\nvar _c;\n$RefreshReg$(_c, \"TryForFreeModal\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "TbX", "TbBrain", "TbSchool", "TbUser", "TbArrowRight", "message", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TryForFreeModal", "isOpen", "onClose", "onSubmit", "_s", "_classOptions$formDat", "formData", "setFormData", "name", "level", "class", "loading", "setLoading", "levelOptions", "value", "label", "description", "icon", "classOptions", "primary", "secondary", "advance", "handleInputChange", "field", "prev", "handleSubmit", "e", "preventDefault", "trim", "error", "modalVariants", "hidden", "opacity", "scale", "y", "visible", "transition", "type", "damping", "stiffness", "exit", "duration", "overlayVariants", "console", "log", "className", "style", "zIndex", "position", "top", "left", "right", "bottom", "backgroundColor", "children", "padding", "borderRadius", "max<PERSON><PERSON><PERSON>", "width", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "cursor", "onChange", "target", "placeholder", "map", "c", "disabled", "max<PERSON><PERSON><PERSON>", "button", "whileHover", "whileTap", "div", "initial", "height", "animate", "classOption", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/TryForFreeModal.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbX, Tb<PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TbArrowRight } from \"react-icons/tb\";\nimport { message } from \"antd\";\n\nconst TryForFreeModal = ({ isOpen, onClose, onSubmit }) => {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    level: \"\",\n    class: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n\n  // Level and class configurations\n  const levelOptions = [\n    { value: \"primary\", label: \"Primary\", description: \"Classes 1-7\", icon: \"🌱\" },\n    { value: \"secondary\", label: \"Secondary\", description: \"Form 1-4\", icon: \"📚\" },\n    { value: \"advance\", label: \"Advance\", description: \"Form 5-6\", icon: \"🎓\" }\n  ];\n\n  const classOptions = {\n    primary: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"],\n    secondary: [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"],\n    advance: [\"Form-5\", \"Form-6\"]\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value,\n      // Reset class when level changes\n      ...(field === \"level\" && { class: \"\" })\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.name.trim()) {\n      message.error(\"Please enter your name\");\n      return;\n    }\n    \n    if (!formData.level) {\n      message.error(\"Please select your level\");\n      return;\n    }\n    \n    if (!formData.class) {\n      message.error(\"Please select your class\");\n      return;\n    }\n\n    setLoading(true);\n    try {\n      await onSubmit(formData);\n    } catch (error) {\n      message.error(\"Something went wrong. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const modalVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 50 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: { \n        type: \"spring\", \n        damping: 25, \n        stiffness: 300 \n      }\n    },\n    exit: { \n      opacity: 0, \n      scale: 0.8, \n      y: 50,\n      transition: { duration: 0.2 }\n    }\n  };\n\n  const overlayVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  console.log(\"TryForFreeModal render check - isOpen:\", isOpen);\n\n  if (!isOpen) {\n    console.log(\"Modal not rendering because isOpen is false\");\n    return null;\n  }\n\n  console.log(\"Modal should be rendering now!\");\n\n  return (\n    <div\n      className=\"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50\"\n      style={{\n        zIndex: 9999,\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0,0,0,0.5)'\n      }}\n    >\n      <div\n        className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl\"\n        style={{\n          backgroundColor: 'white',\n          padding: '24px',\n          borderRadius: '8px',\n          maxWidth: '400px',\n          width: '90%'\n        }}\n      >\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-bold\" style={{ color: 'black', fontSize: '24px' }}>\n            🎉 MODAL IS WORKING! 🎉\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-500 hover:text-gray-700\"\n            style={{ fontSize: '20px', cursor: 'pointer' }}\n          >\n            ✕\n          </button>\n        </div>\n        <p className=\"mb-4\" style={{ color: 'black' }}>\n          If you can see this, the modal is working! Experience our platform with a sample quiz.\n        </p>\n\n        <div>\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium mb-2\">Your Name</label>\n            <input\n              type=\"text\"\n              value={formData.name}\n              onChange={(e) => handleInputChange(\"name\", e.target.value)}\n              className=\"w-full px-3 py-2 border rounded-lg\"\n              placeholder=\"Enter your name\"\n            />\n          </div>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium mb-2\">Level</label>\n            <select\n              value={formData.level}\n              onChange={(e) => handleInputChange(\"level\", e.target.value)}\n              className=\"w-full px-3 py-2 border rounded-lg\"\n            >\n              <option value=\"\">Select Level</option>\n              <option value=\"primary\">Primary</option>\n              <option value=\"secondary\">Secondary</option>\n              <option value=\"advance\">Advance</option>\n            </select>\n          </div>\n\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium mb-2\">Class</label>\n            <select\n              value={formData.class}\n              onChange={(e) => handleInputChange(\"class\", e.target.value)}\n              className=\"w-full px-3 py-2 border rounded-lg\"\n            >\n              <option value=\"\">Select Class</option>\n              {formData.level === \"primary\" && [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"].map(c => (\n                <option key={c} value={c}>Class {c}</option>\n              ))}\n              {formData.level === \"secondary\" && [\"Form-1\", \"Form-2\", \"Form-3\", \"Form-4\"].map(c => (\n                <option key={c} value={c}>{c}</option>\n              ))}\n              {formData.level === \"advance\" && [\"Form-5\", \"Form-6\"].map(c => (\n                <option key={c} value={c}>{c}</option>\n              ))}\n            </select>\n          </div>\n\n          <button\n            onClick={handleSubmit}\n            disabled={loading}\n            className=\"w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n          >\n            {loading ? \"Loading...\" : \"Start Free Trial\"}\n          </button>\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-4 text-white\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2 sm:space-x-3\">\n                <div className=\"p-2 bg-white/20 rounded-lg\">\n                  <TbBrain className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                </div>\n                <div>\n                  <h2 className=\"text-lg sm:text-xl font-bold\">Try BrainWave Free</h2>\n                  <p className=\"text-blue-100 text-xs sm:text-sm\">Experience our platform with a sample quiz</p>\n                </div>\n              </div>\n              <button\n                onClick={onClose}\n                className=\"p-2 hover:bg-white/20 rounded-lg transition-colors\"\n              >\n                <TbX className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Form */}\n          <form onSubmit={handleSubmit} className=\"p-4 sm:p-6 space-y-4 sm:space-y-6\">\n            {/* Name Input */}\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center space-x-2 text-sm font-medium text-gray-700\">\n                <TbUser className=\"w-4 h-4\" />\n                <span>Your Name</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.name}\n                onChange={(e) => handleInputChange(\"name\", e.target.value)}\n                placeholder=\"Enter your full name\"\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\n                maxLength={50}\n              />\n            </div>\n\n            {/* Level Selection */}\n            <div className=\"space-y-3\">\n              <label className=\"flex items-center space-x-2 text-sm font-medium text-gray-700\">\n                <TbSchool className=\"w-4 h-4\" />\n                <span>Education Level</span>\n              </label>\n              <div className=\"grid gap-3\">\n                {levelOptions.map((level) => (\n                  <motion.button\n                    key={level.value}\n                    type=\"button\"\n                    onClick={() => handleInputChange(\"level\", level.value)}\n                    className={`p-4 border-2 rounded-lg text-left transition-all ${\n                      formData.level === level.value\n                        ? \"border-blue-500 bg-blue-50 text-blue-700\"\n                        : \"border-gray-200 hover:border-blue-300 hover:bg-blue-50\"\n                    }`}\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-2xl\">{level.icon}</span>\n                      <div>\n                        <div className=\"font-medium\">{level.label}</div>\n                        <div className=\"text-sm text-gray-500\">{level.description}</div>\n                      </div>\n                    </div>\n                  </motion.button>\n                ))}\n              </div>\n            </div>\n\n            {/* Class Selection */}\n            {formData.level && (\n              <motion.div\n                className=\"space-y-3\"\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: \"auto\" }}\n                transition={{ duration: 0.3 }}\n              >\n                <label className=\"text-sm font-medium text-gray-700\">\n                  Select Your Class\n                </label>\n                <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-2\">\n                  {classOptions[formData.level]?.map((classOption) => (\n                    <motion.button\n                      key={classOption}\n                      type=\"button\"\n                      onClick={() => handleInputChange(\"class\", classOption)}\n                      className={`p-2 sm:p-3 border-2 rounded-lg text-center font-medium transition-all text-sm sm:text-base ${\n                        formData.class === classOption\n                          ? \"border-blue-500 bg-blue-500 text-white\"\n                          : \"border-gray-200 hover:border-blue-300 hover:bg-blue-50\"\n                      }`}\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      {classOption}\n                    </motion.button>\n                  ))}\n                </div>\n              </motion.div>\n            )}\n\n            {/* Submit Button */}\n            <motion.button\n              type=\"submit\"\n              disabled={loading || !formData.name || !formData.level || !formData.class}\n              className={`w-full py-4 px-6 rounded-lg font-medium flex items-center justify-center space-x-2 transition-all ${\n                loading || !formData.name || !formData.level || !formData.class\n                  ? \"bg-gray-300 text-gray-500 cursor-not-allowed\"\n                  : \"bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl\"\n              }`}\n              whileHover={!loading && formData.name && formData.level && formData.class ? { scale: 1.02 } : {}}\n              whileTap={!loading && formData.name && formData.level && formData.class ? { scale: 0.98 } : {}}\n            >\n              {loading ? (\n                <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\" />\n              ) : (\n                <>\n                  <span>Start Free Trial</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </>\n              )}\n            </motion.button>\n\n            {/* Info Text */}\n            <div className=\"text-center text-sm text-gray-500\">\n              <p>No registration required for trial</p>\n              <p className=\"mt-1\">Experience one quiz to see what BrainWave offers</p>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TryForFreeModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,QAAQ,gBAAgB;AAC7E,SAASC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACzD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM0B,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC9E;IAAEH,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE,WAAW;IAAEC,WAAW,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC/E;IAAEH,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,WAAW,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,CAC5E;EAED,MAAMC,YAAY,GAAG;IACnBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC5CC,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACnDC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ;EAC9B,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAET,KAAK,KAAK;IAC1CP,WAAW,CAACiB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACD,KAAK,GAAGT,KAAK;MACd;MACA,IAAIS,KAAK,KAAK,OAAO,IAAI;QAAEb,KAAK,EAAE;MAAG,CAAC;IACxC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMe,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACrB,QAAQ,CAACE,IAAI,CAACoB,IAAI,CAAC,CAAC,EAAE;MACzBjC,OAAO,CAACkC,KAAK,CAAC,wBAAwB,CAAC;MACvC;IACF;IAEA,IAAI,CAACvB,QAAQ,CAACG,KAAK,EAAE;MACnBd,OAAO,CAACkC,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;IAEA,IAAI,CAACvB,QAAQ,CAACI,KAAK,EAAE;MACnBf,OAAO,CAACkC,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;IAEAjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMT,QAAQ,CAACG,QAAQ,CAAC;IAC1B,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,yCAAyC,CAAC;IAC1D,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,aAAa,GAAG;IACpBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAG,CAAC;IACzCC,OAAO,EAAE;MACPH,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC;MACRC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QACVC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,GAAG;MACVC,CAAC,EAAE,EAAE;MACLE,UAAU,EAAE;QAAEK,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBX,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBG,OAAO,EAAE;MAAEH,OAAO,EAAE;IAAE,CAAC;IACvBQ,IAAI,EAAE;MAAER,OAAO,EAAE;IAAE;EACrB,CAAC;EAEDW,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE3C,MAAM,CAAC;EAE7D,IAAI,CAACA,MAAM,EAAE;IACX0C,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D,OAAO,IAAI;EACb;EAEAD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAE7C,oBACE/C,OAAA;IACEgD,SAAS,EAAC,uEAAuE;IACjFC,KAAK,EAAE;MACLC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,eAEFzD,OAAA;MACEgD,SAAS,EAAC,wDAAwD;MAClEC,KAAK,EAAE;QACLO,eAAe,EAAE,OAAO;QACxBE,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE;MACT,CAAE;MAAAJ,QAAA,gBAEFzD,OAAA;QAAKgD,SAAS,EAAC,wCAAwC;QAAAS,QAAA,gBACrDzD,OAAA;UAAIgD,SAAS,EAAC,mBAAmB;UAACC,KAAK,EAAE;YAAEa,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAE/E;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnE,OAAA;UACEoE,OAAO,EAAE/D,OAAQ;UACjB2C,SAAS,EAAC,mCAAmC;UAC7CC,KAAK,EAAE;YAAEc,QAAQ,EAAE,MAAM;YAAEM,MAAM,EAAE;UAAU,CAAE;UAAAZ,QAAA,EAChD;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnE,OAAA;QAAGgD,SAAS,EAAC,MAAM;QAACC,KAAK,EAAE;UAAEa,KAAK,EAAE;QAAQ,CAAE;QAAAL,QAAA,EAAC;MAE/C;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJnE,OAAA;QAAAyD,QAAA,gBACEzD,OAAA;UAAKgD,SAAS,EAAC,MAAM;UAAAS,QAAA,gBACnBzD,OAAA;YAAOgD,SAAS,EAAC,gCAAgC;YAAAS,QAAA,EAAC;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnEnE,OAAA;YACEwC,IAAI,EAAC,MAAM;YACXvB,KAAK,EAAER,QAAQ,CAACE,IAAK;YACrB2D,QAAQ,EAAGzC,CAAC,IAAKJ,iBAAiB,CAAC,MAAM,EAAEI,CAAC,CAAC0C,MAAM,CAACtD,KAAK,CAAE;YAC3D+B,SAAS,EAAC,oCAAoC;YAC9CwB,WAAW,EAAC;UAAiB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnE,OAAA;UAAKgD,SAAS,EAAC,MAAM;UAAAS,QAAA,gBACnBzD,OAAA;YAAOgD,SAAS,EAAC,gCAAgC;YAAAS,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/DnE,OAAA;YACEiB,KAAK,EAAER,QAAQ,CAACG,KAAM;YACtB0D,QAAQ,EAAGzC,CAAC,IAAKJ,iBAAiB,CAAC,OAAO,EAAEI,CAAC,CAAC0C,MAAM,CAACtD,KAAK,CAAE;YAC5D+B,SAAS,EAAC,oCAAoC;YAAAS,QAAA,gBAE9CzD,OAAA;cAAQiB,KAAK,EAAC,EAAE;cAAAwC,QAAA,EAAC;YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCnE,OAAA;cAAQiB,KAAK,EAAC,SAAS;cAAAwC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCnE,OAAA;cAAQiB,KAAK,EAAC,WAAW;cAAAwC,QAAA,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5CnE,OAAA;cAAQiB,KAAK,EAAC,SAAS;cAAAwC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnE,OAAA;UAAKgD,SAAS,EAAC,MAAM;UAAAS,QAAA,gBACnBzD,OAAA;YAAOgD,SAAS,EAAC,gCAAgC;YAAAS,QAAA,EAAC;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/DnE,OAAA;YACEiB,KAAK,EAAER,QAAQ,CAACI,KAAM;YACtByD,QAAQ,EAAGzC,CAAC,IAAKJ,iBAAiB,CAAC,OAAO,EAAEI,CAAC,CAAC0C,MAAM,CAACtD,KAAK,CAAE;YAC5D+B,SAAS,EAAC,oCAAoC;YAAAS,QAAA,gBAE9CzD,OAAA;cAAQiB,KAAK,EAAC,EAAE;cAAAwC,QAAA,EAAC;YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACrC1D,QAAQ,CAACG,KAAK,KAAK,SAAS,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC6D,GAAG,CAACC,CAAC,iBACxE1E,OAAA;cAAgBiB,KAAK,EAAEyD,CAAE;cAAAjB,QAAA,GAAC,QAAM,EAACiB,CAAC;YAAA,GAArBA,CAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA6B,CAC5C,CAAC,EACD1D,QAAQ,CAACG,KAAK,KAAK,WAAW,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC6D,GAAG,CAACC,CAAC,iBAC/E1E,OAAA;cAAgBiB,KAAK,EAAEyD,CAAE;cAAAjB,QAAA,EAAEiB;YAAC,GAAfA,CAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuB,CACtC,CAAC,EACD1D,QAAQ,CAACG,KAAK,KAAK,SAAS,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC6D,GAAG,CAACC,CAAC,iBACzD1E,OAAA;cAAgBiB,KAAK,EAAEyD,CAAE;cAAAjB,QAAA,EAAEiB;YAAC,GAAfA,CAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuB,CACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnE,OAAA;UACEoE,OAAO,EAAExC,YAAa;UACtB+C,QAAQ,EAAE7D,OAAQ;UAClBkC,SAAS,EAAC,0FAA0F;UAAAS,QAAA,EAEnG3C,OAAO,GAAG,YAAY,GAAG;QAAkB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAETnE,OAAA;UAAKgD,SAAS,EAAC,yEAAyE;UAAAS,QAAA,eACtFzD,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAS,QAAA,gBAChDzD,OAAA;cAAKgD,SAAS,EAAC,0CAA0C;cAAAS,QAAA,gBACvDzD,OAAA;gBAAKgD,SAAS,EAAC,4BAA4B;gBAAAS,QAAA,eACzCzD,OAAA,CAACN,OAAO;kBAACsD,SAAS,EAAC;gBAAuB;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNnE,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAIgD,SAAS,EAAC,8BAA8B;kBAAAS,QAAA,EAAC;gBAAkB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEnE,OAAA;kBAAGgD,SAAS,EAAC,kCAAkC;kBAAAS,QAAA,EAAC;gBAA0C;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnE,OAAA;cACEoE,OAAO,EAAE/D,OAAQ;cACjB2C,SAAS,EAAC,oDAAoD;cAAAS,QAAA,eAE9DzD,OAAA,CAACP,GAAG;gBAACuD,SAAS,EAAC;cAAuB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnE,OAAA;UAAMM,QAAQ,EAAEsB,YAAa;UAACoB,SAAS,EAAC,mCAAmC;UAAAS,QAAA,gBAEzEzD,OAAA;YAAKgD,SAAS,EAAC,WAAW;YAAAS,QAAA,gBACxBzD,OAAA;cAAOgD,SAAS,EAAC,+DAA+D;cAAAS,QAAA,gBAC9EzD,OAAA,CAACJ,MAAM;gBAACoD,SAAS,EAAC;cAAS;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BnE,OAAA;gBAAAyD,QAAA,EAAM;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACRnE,OAAA;cACEwC,IAAI,EAAC,MAAM;cACXvB,KAAK,EAAER,QAAQ,CAACE,IAAK;cACrB2D,QAAQ,EAAGzC,CAAC,IAAKJ,iBAAiB,CAAC,MAAM,EAAEI,CAAC,CAAC0C,MAAM,CAACtD,KAAK,CAAE;cAC3DuD,WAAW,EAAC,sBAAsB;cAClCxB,SAAS,EAAC,6HAA6H;cACvI4B,SAAS,EAAE;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnE,OAAA;YAAKgD,SAAS,EAAC,WAAW;YAAAS,QAAA,gBACxBzD,OAAA;cAAOgD,SAAS,EAAC,+DAA+D;cAAAS,QAAA,gBAC9EzD,OAAA,CAACL,QAAQ;gBAACqD,SAAS,EAAC;cAAS;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCnE,OAAA;gBAAAyD,QAAA,EAAM;cAAe;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACRnE,OAAA;cAAKgD,SAAS,EAAC,YAAY;cAAAS,QAAA,EACxBzC,YAAY,CAACyD,GAAG,CAAE7D,KAAK,iBACtBZ,OAAA,CAACT,MAAM,CAACsF,MAAM;gBAEZrC,IAAI,EAAC,QAAQ;gBACb4B,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAAC,OAAO,EAAEb,KAAK,CAACK,KAAK,CAAE;gBACvD+B,SAAS,EAAG,oDACVvC,QAAQ,CAACG,KAAK,KAAKA,KAAK,CAACK,KAAK,GAC1B,0CAA0C,GAC1C,wDACL,EAAE;gBACH6D,UAAU,EAAE;kBAAE1C,KAAK,EAAE;gBAAK,CAAE;gBAC5B2C,QAAQ,EAAE;kBAAE3C,KAAK,EAAE;gBAAK,CAAE;gBAAAqB,QAAA,eAE1BzD,OAAA;kBAAKgD,SAAS,EAAC,6BAA6B;kBAAAS,QAAA,gBAC1CzD,OAAA;oBAAMgD,SAAS,EAAC,UAAU;oBAAAS,QAAA,EAAE7C,KAAK,CAACQ;kBAAI;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9CnE,OAAA;oBAAAyD,QAAA,gBACEzD,OAAA;sBAAKgD,SAAS,EAAC,aAAa;sBAAAS,QAAA,EAAE7C,KAAK,CAACM;oBAAK;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChDnE,OAAA;sBAAKgD,SAAS,EAAC,uBAAuB;sBAAAS,QAAA,EAAE7C,KAAK,CAACO;oBAAW;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAjBDvD,KAAK,CAACK,KAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBH,CAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL1D,QAAQ,CAACG,KAAK,iBACbZ,OAAA,CAACT,MAAM,CAACyF,GAAG;YACThC,SAAS,EAAC,WAAW;YACrBiC,OAAO,EAAE;cAAE9C,OAAO,EAAE,CAAC;cAAE+C,MAAM,EAAE;YAAE,CAAE;YACnCC,OAAO,EAAE;cAAEhD,OAAO,EAAE,CAAC;cAAE+C,MAAM,EAAE;YAAO,CAAE;YACxC3C,UAAU,EAAE;cAAEK,QAAQ,EAAE;YAAI,CAAE;YAAAa,QAAA,gBAE9BzD,OAAA;cAAOgD,SAAS,EAAC,mCAAmC;cAAAS,QAAA,EAAC;YAErD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnE,OAAA;cAAKgD,SAAS,EAAC,uCAAuC;cAAAS,QAAA,GAAAjD,qBAAA,GACnDa,YAAY,CAACZ,QAAQ,CAACG,KAAK,CAAC,cAAAJ,qBAAA,uBAA5BA,qBAAA,CAA8BiE,GAAG,CAAEW,WAAW,iBAC7CpF,OAAA,CAACT,MAAM,CAACsF,MAAM;gBAEZrC,IAAI,EAAC,QAAQ;gBACb4B,OAAO,EAAEA,CAAA,KAAM3C,iBAAiB,CAAC,OAAO,EAAE2D,WAAW,CAAE;gBACvDpC,SAAS,EAAG,8FACVvC,QAAQ,CAACI,KAAK,KAAKuE,WAAW,GAC1B,wCAAwC,GACxC,wDACL,EAAE;gBACHN,UAAU,EAAE;kBAAE1C,KAAK,EAAE;gBAAK,CAAE;gBAC5B2C,QAAQ,EAAE;kBAAE3C,KAAK,EAAE;gBAAK,CAAE;gBAAAqB,QAAA,EAEzB2B;cAAW,GAXPA,WAAW;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYH,CAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAGDnE,OAAA,CAACT,MAAM,CAACsF,MAAM;YACZrC,IAAI,EAAC,QAAQ;YACbmC,QAAQ,EAAE7D,OAAO,IAAI,CAACL,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACG,KAAK,IAAI,CAACH,QAAQ,CAACI,KAAM;YAC1EmC,SAAS,EAAG,qGACVlC,OAAO,IAAI,CAACL,QAAQ,CAACE,IAAI,IAAI,CAACF,QAAQ,CAACG,KAAK,IAAI,CAACH,QAAQ,CAACI,KAAK,GAC3D,8CAA8C,GAC9C,uHACL,EAAE;YACHiE,UAAU,EAAE,CAAChE,OAAO,IAAIL,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACI,KAAK,GAAG;cAAEuB,KAAK,EAAE;YAAK,CAAC,GAAG,CAAC,CAAE;YACjG2C,QAAQ,EAAE,CAACjE,OAAO,IAAIL,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACI,KAAK,GAAG;cAAEuB,KAAK,EAAE;YAAK,CAAC,GAAG,CAAC,CAAE;YAAAqB,QAAA,EAE9F3C,OAAO,gBACNd,OAAA;cAAKgD,SAAS,EAAC;YAA2E;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE7FnE,OAAA,CAAAE,SAAA;cAAAuD,QAAA,gBACEzD,OAAA;gBAAAyD,QAAA,EAAM;cAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7BnE,OAAA,CAACH,YAAY;gBAACmD,SAAS,EAAC;cAAS;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACpC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CAAC,eAGhBnE,OAAA;YAAKgD,SAAS,EAAC,mCAAmC;YAAAS,QAAA,gBAChDzD,OAAA;cAAAyD,QAAA,EAAG;YAAkC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzCnE,OAAA;cAAGgD,SAAS,EAAC,MAAM;cAAAS,QAAA,EAAC;YAAgD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAhUIJ,eAAe;AAAAkF,EAAA,GAAflF,eAAe;AAkUrB,eAAeA,eAAe;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}