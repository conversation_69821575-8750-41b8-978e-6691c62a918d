import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import TrialQuizSelection from "../../components/trial/TrialQuizSelection";
import TrialQuizPlay from "../../components/trial/TrialQuizPlay";
import TrialQuizResult from "../../components/trial/TrialQuizResult";
import TrialRegistrationPrompt from "../../components/trial/TrialRegistrationPrompt";

const TrialPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  const [currentStep, setCurrentStep] = useState("selection"); // selection, playing, result
  const [trialUserInfo, setTrialUserInfo] = useState(null);
  const [selectedQuiz, setSelectedQuiz] = useState(null);
  const [quizResult, setQuizResult] = useState(null);
  const [showRegistrationPrompt, setShowRegistrationPrompt] = useState(false);

  // Get trial user info from navigation state
  useEffect(() => {
    if (location.state?.trialUserInfo) {
      setTrialUserInfo(location.state.trialUserInfo);
    } else {
      // If no trial user info, redirect to home
      navigate('/');
    }
  }, [location.state, navigate]);

  // Handle quiz selection
  const handleQuizSelected = (quizData) => {
    setSelectedQuiz(quizData);
    setCurrentStep("playing");
  };

  // Handle quiz completion
  const handleQuizComplete = (result) => {
    setQuizResult(result);
    setCurrentStep("result");
    
    // Show registration prompt after a short delay
    setTimeout(() => {
      setShowRegistrationPrompt(true);
    }, 3000);
  };

  // Handle back navigation
  const handleBack = () => {
    if (currentStep === "playing") {
      setCurrentStep("selection");
      setSelectedQuiz(null);
    } else if (currentStep === "selection") {
      navigate('/');
    }
  };

  // Handle try another quiz
  const handleTryAnother = () => {
    setCurrentStep("selection");
    setSelectedQuiz(null);
    setQuizResult(null);
    setShowRegistrationPrompt(false);
  };

  // Handle registration
  const handleRegister = () => {
    navigate('/register', { 
      state: { 
        trialCompleted: true,
        trialResult: quizResult,
        trialUserInfo: trialUserInfo
      }
    });
  };

  if (!trialUserInfo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
          <p className="text-gray-600">Loading trial experience...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="trial-page">
      {/* Step Indicator */}
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40">
        <div className="bg-white/90 backdrop-blur-sm rounded-full px-6 py-2 shadow-lg border">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${
              currentStep === "selection" ? "text-blue-600" : "text-gray-400"
            }`}>
              <div className={`w-3 h-3 rounded-full ${
                currentStep === "selection" ? "bg-blue-600" : "bg-gray-300"
              }`}></div>
              <span className="text-sm font-medium">Select Quiz</span>
            </div>
            
            <div className="w-8 h-px bg-gray-300"></div>
            
            <div className={`flex items-center space-x-2 ${
              currentStep === "playing" ? "text-blue-600" : "text-gray-400"
            }`}>
              <div className={`w-3 h-3 rounded-full ${
                currentStep === "playing" ? "bg-blue-600" : "bg-gray-300"
              }`}></div>
              <span className="text-sm font-medium">Take Quiz</span>
            </div>
            
            <div className="w-8 h-px bg-gray-300"></div>
            
            <div className={`flex items-center space-x-2 ${
              currentStep === "result" ? "text-blue-600" : "text-gray-400"
            }`}>
              <div className={`w-3 h-3 rounded-full ${
                currentStep === "result" ? "bg-blue-600" : "bg-gray-300"
              }`}></div>
              <span className="text-sm font-medium">Results</span>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <motion.div
        key={currentStep}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        transition={{ duration: 0.3 }}
      >
        {currentStep === "selection" && (
          <TrialQuizSelection
            trialUserInfo={trialUserInfo}
            onQuizSelected={handleQuizSelected}
            onBack={handleBack}
          />
        )}

        {currentStep === "playing" && selectedQuiz && (
          <TrialQuizPlay
            quizData={selectedQuiz}
            onComplete={handleQuizComplete}
            onBack={handleBack}
          />
        )}

        {currentStep === "result" && quizResult && (
          <TrialQuizResult
            result={quizResult}
            onTryAnother={handleTryAnother}
            onRegister={handleRegister}
          />
        )}
      </motion.div>

      {/* Registration Prompt Modal */}
      <TrialRegistrationPrompt
        isOpen={showRegistrationPrompt}
        onClose={() => setShowRegistrationPrompt(false)}
        trialResult={quizResult}
      />
    </div>
  );
};

export default TrialPage;
