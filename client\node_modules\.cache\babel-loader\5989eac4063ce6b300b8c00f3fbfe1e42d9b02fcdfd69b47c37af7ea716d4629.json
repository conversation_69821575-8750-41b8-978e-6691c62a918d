{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialQuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { TbTrophy, TbCheck, TbX, TbClock, TbBrain, TbArrowRight, TbStar, TbUsers, TbBook, TbMessageCircle, TbChartBar } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialQuizResult = ({\n  result,\n  onTryAnother,\n  onRegister\n}) => {\n  _s();\n  var _result$questionResul;\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n  const getPerformanceMessage = percentage => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n  const premiumFeatures = [{\n    icon: TbBook,\n    title: \"Study Materials\",\n    description: \"Access comprehensive study materials, notes, and resources\"\n  }, {\n    icon: TbBrain,\n    title: \"AI Assistant\",\n    description: \"Get personalized explanations and study recommendations\"\n  }, {\n    icon: TbChartBar,\n    title: \"Ranking System\",\n    description: \"Compete with other students and track your progress\"\n  }, {\n    icon: TbMessageCircle,\n    title: \"Forum Access\",\n    description: \"Ask questions and help other students in our community\"\n  }, {\n    icon: TbUsers,\n    title: \"Unlimited Quizzes\",\n    description: \"Take as many quizzes as you want across all subjects\"\n  }, {\n    icon: TbStar,\n    title: \"Progress Tracking\",\n    description: \"Detailed analytics and performance insights\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-4 sm:py-8 px-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-6 sm:mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 rounded-full ${performance.bg} mb-4`,\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: `w-8 h-8 sm:w-10 sm:h-10 ${performance.color}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2\",\n          children: \"Trial Complete!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-lg sm:text-xl font-semibold ${performance.color}`,\n          children: performance.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.2\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-4 sm:p-8 mb-6 sm:mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 bg-blue-50 rounded-xl\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl sm:text-3xl font-bold text-blue-600\",\n              children: [result.percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm text-gray-600\",\n              children: \"Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 bg-green-50 rounded-xl\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl sm:text-3xl font-bold text-green-600\",\n              children: result.correctAnswers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm text-gray-600\",\n              children: \"Correct\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 bg-red-50 rounded-xl\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl sm:text-3xl font-bold text-red-600\",\n              children: result.wrongAnswers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm text-gray-600\",\n              children: \"Wrong\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 sm:p-4 bg-purple-50 rounded-xl\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl sm:text-3xl font-bold text-purple-600\",\n              children: formatTime(result.timeSpent)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm text-gray-600\",\n              children: \"Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `inline-flex items-center space-x-2 px-4 py-2 rounded-full ${result.passed ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`,\n            children: [result.passed ? /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 32\n            }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 66\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [result.passed ? 'Passed' : 'Not Passed', \" \\u2022 \", result.examName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowDetails(!showDetails),\n            className: \"text-blue-600 hover:text-blue-700 font-medium\",\n            children: showDetails ? 'Hide Details' : 'Show Question Details'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          height: 0\n        },\n        animate: {\n          opacity: 1,\n          height: \"auto\"\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"bg-white rounded-2xl shadow-xl p-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-gray-800 mb-4\",\n          children: \"Question Review\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: (_result$questionResul = result.questionResults) === null || _result$questionResul === void 0 ? void 0 : _result$questionResul.map((q, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-4 rounded-lg border-2 ${q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center ${q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                children: q.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 38\n                }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 72\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-800 mb-2\",\n                  children: q.question\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Your answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`,\n                      children: q.userAnswer || 'Not answered'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 25\n                  }, this), !q.isCorrect && /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-600\",\n                      children: \"Correct answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2 font-medium text-green-600\",\n                      children: q.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.4\n        },\n        className: \"bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl shadow-xl p-8 text-white mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl md:text-3xl font-bold mb-2\",\n            children: \"Unlock Your Full Potential! \\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-100 text-lg\",\n            children: \"Register now to access all premium features and accelerate your learning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n          children: premiumFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.3,\n              delay: 0.1 * index\n            },\n            className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4\",\n            children: [/*#__PURE__*/_jsxDEV(feature.icon, {\n              className: \"w-8 h-8 text-blue-200 mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold mb-2\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-blue-100\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                className: \"px-8 py-4 bg-white text-blue-600 rounded-xl font-bold text-lg hover:bg-blue-50 transition-all shadow-lg hover:shadow-xl flex items-center space-x-2\",\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Register Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-8 py-4 border-2 border-white text-white rounded-xl font-bold text-lg hover:bg-white/10 transition-all\",\n                children: \"Already Have Account?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.6\n        },\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onTryAnother,\n          className: \"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n          children: \"Try Another Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\",\n            children: \"Back to Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\",\n          children: \"\\uD83C\\uDFAF Trial Mode - Register for unlimited access\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(TrialQuizResult, \"4yqwfDzoJfQxBqvC4gGtnTfiTt0=\");\n_c = TrialQuizResult;\nexport default TrialQuizResult;\nvar _c;\n$RefreshReg$(_c, \"TrialQuizResult\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "TbTrophy", "TbCheck", "TbX", "TbClock", "TbBrain", "TbArrowRight", "TbStar", "TbUsers", "TbBook", "TbMessageCircle", "TbChartBar", "jsxDEV", "_jsxDEV", "TrialQuizResult", "result", "onTryAnother", "onRegister", "_s", "_result$questionResul", "showDetails", "setShowDetails", "animationComplete", "setAnimationComplete", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "getPerformanceMessage", "percentage", "message", "color", "bg", "gradient", "performance", "isPassed", "premiumFeatures", "icon", "title", "description", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scale", "delay", "correctAnswers", "wrongAnswers", "timeSpent", "passed", "examName", "onClick", "height", "questionResults", "map", "q", "index", "isCorrect", "question", "userAnswer", "<PERSON><PERSON><PERSON><PERSON>", "feature", "to", "button", "whileHover", "whileTap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialQuizResult.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { \n  TbTrophy, \n  TbCheck, \n  TbX, \n  TbClock, \n  TbBrain, \n  TbArrowRight,\n  TbStar,\n  TbUsers,\n  TbBook,\n  TbMessageCircle,\n  TbChartBar\n} from \"react-icons/tb\";\n\nconst TrialQuizResult = ({ result, onTryAnother, onRegister }) => {\n  const [showDetails, setShowDetails] = useState(false);\n  const [animationComplete, setAnimationComplete] = useState(false);\n\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}m ${remainingSeconds}s`;\n  };\n\n  const getPerformanceMessage = (percentage) => {\n    if (percentage >= 90) return {\n      message: \"Outstanding Performance! 🌟\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\",\n      gradient: \"from-purple-500 to-purple-600\"\n    };\n    if (percentage >= 80) return {\n      message: \"Excellent Work! 🎉\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\",\n      gradient: \"from-green-500 to-green-600\"\n    };\n    if (percentage >= 70) return {\n      message: \"Great Job! 👏\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\",\n      gradient: \"from-blue-500 to-blue-600\"\n    };\n    if (percentage >= 60) return {\n      message: \"Well Done! ✨\",\n      color: \"text-emerald-600\",\n      bg: \"bg-emerald-50\",\n      gradient: \"from-emerald-500 to-emerald-600\"\n    };\n    if (percentage >= 40) return {\n      message: \"Good Effort! 💪\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\",\n      gradient: \"from-yellow-500 to-yellow-600\"\n    };\n    return {\n      message: \"Keep Practicing! 📚\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\",\n      gradient: \"from-orange-500 to-orange-600\"\n    };\n  };\n\n  const performance = getPerformanceMessage(result.percentage);\n  const isPassed = result.percentage >= 60;\n\n  const premiumFeatures = [\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Access comprehensive study materials, notes, and resources\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\"\n    },\n    {\n      icon: TbChartBar,\n      title: \"Ranking System\",\n      description: \"Compete with other students and track your progress\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\"\n    },\n    {\n      icon: TbUsers,\n      title: \"Unlimited Quizzes\",\n      description: \"Take as many quizzes as you want across all subjects\"\n    },\n    {\n      icon: TbStar,\n      title: \"Progress Tracking\",\n      description: \"Detailed analytics and performance insights\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-4 sm:py-8 px-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Result Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-6 sm:mb-8\"\n        >\n          <div className={`inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 rounded-full ${performance.bg} mb-4`}>\n            <TbTrophy className={`w-8 h-8 sm:w-10 sm:h-10 ${performance.color}`} />\n          </div>\n          <h1 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2\">\n            Trial Complete!\n          </h1>\n          <p className={`text-lg sm:text-xl font-semibold ${performance.color}`}>\n            {performance.message}\n          </p>\n        </motion.div>\n\n        {/* Score Card */}\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          className=\"bg-white rounded-2xl shadow-xl p-4 sm:p-8 mb-6 sm:mb-8\"\n        >\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 text-center\">\n            <div className=\"p-3 sm:p-4 bg-blue-50 rounded-xl\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-blue-600\">{result.percentage}%</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Score</div>\n            </div>\n\n            <div className=\"p-3 sm:p-4 bg-green-50 rounded-xl\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-green-600\">{result.correctAnswers}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Correct</div>\n            </div>\n\n            <div className=\"p-3 sm:p-4 bg-red-50 rounded-xl\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-red-600\">{result.wrongAnswers}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Wrong</div>\n            </div>\n\n            <div className=\"p-3 sm:p-4 bg-purple-50 rounded-xl\">\n              <div className=\"text-2xl sm:text-3xl font-bold text-purple-600\">{formatTime(result.timeSpent)}</div>\n              <div className=\"text-xs sm:text-sm text-gray-600\">Time</div>\n            </div>\n          </div>\n\n          {/* Pass/Fail Status */}\n          <div className=\"mt-6 text-center\">\n            <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${\n              result.passed \n                ? 'bg-green-100 text-green-700' \n                : 'bg-red-100 text-red-700'\n            }`}>\n              {result.passed ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n              <span className=\"font-medium\">\n                {result.passed ? 'Passed' : 'Not Passed'} • {result.examName}\n              </span>\n            </div>\n          </div>\n\n          {/* Show Details Button */}\n          <div className=\"mt-6 text-center\">\n            <button\n              onClick={() => setShowDetails(!showDetails)}\n              className=\"text-blue-600 hover:text-blue-700 font-medium\"\n            >\n              {showDetails ? 'Hide Details' : 'Show Question Details'}\n            </button>\n          </div>\n        </motion.div>\n\n        {/* Question Details */}\n        {showDetails && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl p-6 mb-8\"\n          >\n            <h3 className=\"text-xl font-bold text-gray-800 mb-4\">Question Review</h3>\n            <div className=\"space-y-4\">\n              {result.questionResults?.map((q, index) => (\n                <div key={index} className={`p-4 rounded-lg border-2 ${\n                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'\n                }`}>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\n                    }`}>\n                      {q.isCorrect ? <TbCheck className=\"w-4 h-4\" /> : <TbX className=\"w-4 h-4\" />}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium text-gray-800 mb-2\">{q.question}</p>\n                      <div className=\"text-sm space-y-1\">\n                        <p>\n                          <span className=\"text-gray-600\">Your answer:</span>\n                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>\n                            {q.userAnswer || 'Not answered'}\n                          </span>\n                        </p>\n                        {!q.isCorrect && (\n                          <p>\n                            <span className=\"text-gray-600\">Correct answer:</span>\n                            <span className=\"ml-2 font-medium text-green-600\">{q.correctAnswer}</span>\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </motion.div>\n        )}\n\n        {/* Premium Features Showcase */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          className=\"bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl shadow-xl p-8 text-white mb-8\"\n        >\n          <div className=\"text-center mb-8\">\n            <h2 className=\"text-2xl md:text-3xl font-bold mb-2\">\n              Unlock Your Full Potential! 🚀\n            </h2>\n            <p className=\"text-blue-100 text-lg\">\n              Register now to access all premium features and accelerate your learning\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n            {premiumFeatures.map((feature, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3, delay: 0.1 * index }}\n                className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4\"\n              >\n                <feature.icon className=\"w-8 h-8 text-blue-200 mb-3\" />\n                <h3 className=\"font-semibold mb-2\">{feature.title}</h3>\n                <p className=\"text-sm text-blue-100\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link to=\"/register\">\n                <motion.button\n                  className=\"px-8 py-4 bg-white text-blue-600 rounded-xl font-bold text-lg hover:bg-blue-50 transition-all shadow-lg hover:shadow-xl flex items-center space-x-2\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <span>Register Now</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </motion.button>\n              </Link>\n              \n              <Link to=\"/login\">\n                <button className=\"px-8 py-4 border-2 border-white text-white rounded-xl font-bold text-lg hover:bg-white/10 transition-all\">\n                  Already Have Account?\n                </button>\n              </Link>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Action Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n        >\n          <button\n            onClick={onTryAnother}\n            className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\n          >\n            Try Another Quiz\n          </button>\n          \n          <Link to=\"/\">\n            <button className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium\">\n              Back to Home\n            </button>\n          </Link>\n        </motion.div>\n\n        {/* Trial Badge */}\n        <div className=\"text-center mt-8\">\n          <span className=\"inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium\">\n            🎯 Trial Mode - Register for unlimited access\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TrialQuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,eAAe,EACfC,UAAU,QACL,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM0B,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,KAAIG,gBAAiB,GAAE;EAC3C,CAAC;EAED,MAAMC,qBAAqB,GAAIC,UAAU,IAAK;IAC5C,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,6BAA6B;MACtCC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,oBAAoB;MAC7BC,KAAK,EAAE,gBAAgB;MACvBC,EAAE,EAAE,aAAa;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,eAAe;MACxBC,KAAK,EAAE,eAAe;MACtBC,EAAE,EAAE,YAAY;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,cAAc;MACvBC,KAAK,EAAE,kBAAkB;MACzBC,EAAE,EAAE,eAAe;MACnBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIJ,UAAU,IAAI,EAAE,EAAE,OAAO;MAC3BC,OAAO,EAAE,iBAAiB;MAC1BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO;MACLH,OAAO,EAAE,qBAAqB;MAC9BC,KAAK,EAAE,iBAAiB;MACxBC,EAAE,EAAE,cAAc;MAClBC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAGN,qBAAqB,CAACf,MAAM,CAACgB,UAAU,CAAC;EAC5D,MAAMM,QAAQ,GAAGtB,MAAM,CAACgB,UAAU,IAAI,EAAE;EAExC,MAAMO,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE9B,MAAM;IACZ+B,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAElC,OAAO;IACbmC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE5B,UAAU;IAChB6B,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE7B,eAAe;IACrB8B,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE/B,OAAO;IACbgC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEhC,MAAM;IACZiC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE5B,OAAA;IAAK6B,SAAS,EAAC,oFAAoF;IAAAC,QAAA,eACjG9B,OAAA;MAAK6B,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhC9B,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBAEpC9B,OAAA;UAAK6B,SAAS,EAAG,kFAAiFN,WAAW,CAACF,EAAG,OAAO;UAAAS,QAAA,eACtH9B,OAAA,CAACZ,QAAQ;YAACyC,SAAS,EAAG,2BAA0BN,WAAW,CAACH,KAAM;UAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNzC,OAAA;UAAI6B,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAC;QAE9E;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzC,OAAA;UAAG6B,SAAS,EAAG,oCAAmCN,WAAW,CAACH,KAAM,EAAE;UAAAU,QAAA,EACnEP,WAAW,CAACJ;QAAO;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbzC,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAES,KAAK,EAAE;QAAI,CAAE;QACpCP,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAES,KAAK,EAAE;QAAE,CAAE;QAClCN,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEM,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBAElE9B,OAAA;UAAK6B,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACzE9B,OAAA;YAAK6B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C9B,OAAA;cAAK6B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAAE5B,MAAM,CAACgB,UAAU,EAAC,GAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxFzC,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAENzC,OAAA;YAAK6B,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9B,OAAA;cAAK6B,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAE5B,MAAM,CAAC0C;YAAc;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5FzC,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAENzC,OAAA;YAAK6B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C9B,OAAA;cAAK6B,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAE5B,MAAM,CAAC2C;YAAY;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxFzC,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAENzC,OAAA;YAAK6B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,gBACjD9B,OAAA;cAAK6B,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAEnB,UAAU,CAACT,MAAM,CAAC4C,SAAS;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpGzC,OAAA;cAAK6B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAK6B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B9B,OAAA;YAAK6B,SAAS,EAAG,6DACf3B,MAAM,CAAC6C,MAAM,GACT,6BAA6B,GAC7B,yBACL,EAAE;YAAAjB,QAAA,GACA5B,MAAM,CAAC6C,MAAM,gBAAG/C,OAAA,CAACX,OAAO;cAACwC,SAAS,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACV,GAAG;cAACuC,SAAS,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9EzC,OAAA;cAAM6B,SAAS,EAAC,aAAa;cAAAC,QAAA,GAC1B5B,MAAM,CAAC6C,MAAM,GAAG,QAAQ,GAAG,YAAY,EAAC,UAAG,EAAC7C,MAAM,CAAC8C,QAAQ;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAK6B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B9B,OAAA;YACEiD,OAAO,EAAEA,CAAA,KAAMzC,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5CsB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAExDvB,WAAW,GAAG,cAAc,GAAG;UAAuB;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAGZlC,WAAW,iBACVP,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEiB,MAAM,EAAE;QAAE,CAAE;QACnCf,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEiB,MAAM,EAAE;QAAO,CAAE;QACxCd,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEnD9B,OAAA;UAAI6B,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEzC,OAAA;UAAK6B,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAAxB,qBAAA,GACvBJ,MAAM,CAACiD,eAAe,cAAA7C,qBAAA,uBAAtBA,qBAAA,CAAwB8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACpCtD,OAAA;YAAiB6B,SAAS,EAAG,2BAC3BwB,CAAC,CAACE,SAAS,GAAG,8BAA8B,GAAG,0BAChD,EAAE;YAAAzB,QAAA,eACD9B,OAAA;cAAK6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC9B,OAAA;gBAAK6B,SAAS,EAAG,yDACfwB,CAAC,CAACE,SAAS,GAAG,yBAAyB,GAAG,uBAC3C,EAAE;gBAAAzB,QAAA,EACAuB,CAAC,CAACE,SAAS,gBAAGvD,OAAA,CAACX,OAAO;kBAACwC,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACV,GAAG;kBAACuC,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNzC,OAAA;gBAAK6B,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrB9B,OAAA;kBAAG6B,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAEuB,CAAC,CAACG;gBAAQ;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DzC,OAAA;kBAAK6B,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9B,OAAA;oBAAA8B,QAAA,gBACE9B,OAAA;sBAAM6B,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnDzC,OAAA;sBAAM6B,SAAS,EAAG,oBAAmBwB,CAAC,CAACE,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;sBAAAzB,QAAA,EACpFuB,CAAC,CAACI,UAAU,IAAI;oBAAc;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACH,CAACY,CAAC,CAACE,SAAS,iBACXvD,OAAA;oBAAA8B,QAAA,gBACE9B,OAAA;sBAAM6B,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAe;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtDzC,OAAA;sBAAM6B,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEuB,CAAC,CAACK;oBAAa;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA1BEa,KAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAGDzC,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEM,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBAEhG9B,OAAA;UAAK6B,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B9B,OAAA;YAAI6B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzC,OAAA;YAAG6B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENzC,OAAA;UAAK6B,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EACvEL,eAAe,CAAC2B,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAClCtD,OAAA,CAACd,MAAM,CAAC6C,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEM,KAAK,EAAE,GAAG,GAAGW;YAAM,CAAE;YAClDzB,SAAS,EAAC,6CAA6C;YAAAC,QAAA,gBAEvD9B,OAAA,CAAC2D,OAAO,CAACjC,IAAI;cAACG,SAAS,EAAC;YAA4B;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDzC,OAAA;cAAI6B,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE6B,OAAO,CAAChC;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDzC,OAAA;cAAG6B,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE6B,OAAO,CAAC/B;YAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GARzDa,KAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzC,OAAA;UAAK6B,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1B9B,OAAA;YAAK6B,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D9B,OAAA,CAACb,IAAI;cAACyE,EAAE,EAAC,WAAW;cAAA9B,QAAA,eAClB9B,OAAA,CAACd,MAAM,CAAC2E,MAAM;gBACZhC,SAAS,EAAC,qJAAqJ;gBAC/JiC,UAAU,EAAE;kBAAEpB,KAAK,EAAE;gBAAK,CAAE;gBAC5BqB,QAAQ,EAAE;kBAAErB,KAAK,EAAE;gBAAK,CAAE;gBAAAZ,QAAA,gBAE1B9B,OAAA;kBAAA8B,QAAA,EAAM;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBzC,OAAA,CAACP,YAAY;kBAACoC,SAAS,EAAC;gBAAS;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEPzC,OAAA,CAACb,IAAI;cAACyE,EAAE,EAAC,QAAQ;cAAA9B,QAAA,eACf9B,OAAA;gBAAQ6B,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EAAC;cAE7H;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbzC,OAAA,CAACd,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEM,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE1D9B,OAAA;UACEiD,OAAO,EAAE9C,YAAa;UACtB0B,SAAS,EAAC,0GAA0G;UAAAC,QAAA,EACrH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzC,OAAA,CAACb,IAAI;UAACyE,EAAE,EAAC,GAAG;UAAA9B,QAAA,eACV9B,OAAA;YAAQ6B,SAAS,EAAC,6FAA6F;YAAAC,QAAA,EAAC;UAEhH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGbzC,OAAA;QAAK6B,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B9B,OAAA;UAAM6B,SAAS,EAAC,uFAAuF;UAAAC,QAAA,EAAC;QAExG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CAhSIJ,eAAe;AAAA+D,EAAA,GAAf/D,eAAe;AAkSrB,eAAeA,eAAe;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}