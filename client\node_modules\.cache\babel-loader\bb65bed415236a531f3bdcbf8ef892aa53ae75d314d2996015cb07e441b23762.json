{"ast": null, "code": "import axiosInstance from \"./index\";\n\n// Get trial quiz based on level and class (no authentication required)\nexport const getTrialQuiz = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/trial/get-trial-quiz\", payload);\n    return response.data;\n  } catch (error) {\n    var _error$response;\n    return ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Submit trial quiz results (no authentication required)\nexport const submitTrialResult = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/trial/submit-trial-result\", payload);\n    return response.data;\n  } catch (error) {\n    var _error$response2;\n    return ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};\n\n// Get trial statistics (optional)\nexport const getTrialStats = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/trial/trial-stats\");\n    return response.data;\n  } catch (error) {\n    var _error$response3;\n    return ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || {\n      success: false,\n      message: \"Network error\"\n    };\n  }\n};", "map": {"version": 3, "names": ["axiosInstance", "getTrialQuiz", "payload", "response", "post", "data", "error", "_error$response", "success", "message", "submitTrialResult", "_error$response2", "getTrialStats", "get", "_error$response3"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/trial.js"], "sourcesContent": ["import axiosInstance from \"./index\";\n\n// Get trial quiz based on level and class (no authentication required)\nexport const getTrialQuiz = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/trial/get-trial-quiz\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Submit trial quiz results (no authentication required)\nexport const submitTrialResult = async (payload) => {\n  try {\n    const response = await axiosInstance.post(\"/api/trial/submit-trial-result\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n\n// Get trial statistics (optional)\nexport const getTrialStats = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/trial/trial-stats\");\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Network error\" };\n  }\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,SAAS;;AAEnC;AACA,OAAO,MAAMC,YAAY,GAAG,MAAOC,OAAO,IAAK;EAC7C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMH,aAAa,CAACI,IAAI,CAAC,2BAA2B,EAAEF,OAAO,CAAC;IAC/E,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAC,eAAA;IACd,OAAO,EAAAA,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAG,MAAOR,OAAO,IAAK;EAClD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMH,aAAa,CAACI,IAAI,CAAC,gCAAgC,EAAEF,OAAO,CAAC;IACpF,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAK,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAL,KAAK,CAACH,QAAQ,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBN,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC;;AAED;AACA,OAAO,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACF,MAAMT,QAAQ,GAAG,MAAMH,aAAa,CAACa,GAAG,CAAC,wBAAwB,CAAC;IAClE,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAQ,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAR,KAAK,CAACH,QAAQ,cAAAW,gBAAA,uBAAdA,gBAAA,CAAgBT,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAgB,CAAC;EAC7E;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}