import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { TbBrain, TbClock, TbQuestionMark, TbArrowRight, TbLoader } from "react-icons/tb";
import { message } from "antd";
import { getTrialQuiz } from "../../apicalls/trial";

const TrialQuizSelection = ({ trialUserInfo, onQuizSelected, onBack }) => {
  const [loading, setLoading] = useState(true);
  const [quiz, setQuiz] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchTrialQuiz();
  }, [trialUserInfo]);

  const fetchTrialQuiz = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getTrialQuiz({
        level: trialUserInfo.level,
        class: trialUserInfo.class
      });

      if (response.success) {
        setQuiz(response.data);
        console.log("✅ Trial quiz loaded:", response.data);
      } else {
        setError(response.message || "Failed to load trial quiz");
        message.error(response.message || "Failed to load trial quiz");
      }
    } catch (error) {
      console.error("❌ Error fetching trial quiz:", error);
      setError("Something went wrong. Please try again.");
      message.error("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleStartQuiz = () => {
    if (quiz && quiz.exam) {
      onQuizSelected({
        ...quiz,
        trialUserInfo
      });
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5, delay: 0.2 }
    }
  };

  if (loading) {
    return (
      <motion.div
        className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
            <TbLoader className="w-8 h-8 text-blue-600 animate-spin" />
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Finding Your Perfect Trial Quiz
          </h2>
          <p className="text-gray-600">
            We're selecting a quiz that matches your level and class...
          </p>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <TbQuestionMark className="w-8 h-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Quiz Not Available
          </h2>
          <p className="text-gray-600 mb-6">
            {error}
          </p>
          <div className="space-y-3">
            <button
              onClick={fetchTrialQuiz}
              className="w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Try Again
            </button>
            <button
              onClick={onBack}
              className="w-full py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              Go Back
            </button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-2xl w-full mx-auto">
        {/* Welcome Header */}
        <motion.div
          className="text-center mb-6 sm:mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-2">
            Welcome, {trialUserInfo.name}! 👋
          </h1>
          <p className="text-base sm:text-lg text-gray-600 px-4">
            We've found the perfect quiz for your <span className="font-semibold text-blue-600">{trialUserInfo.level} {trialUserInfo.class}</span> level
          </p>
        </motion.div>

        {/* Quiz Card */}
        <motion.div
          className="bg-white rounded-2xl shadow-xl overflow-hidden"
          variants={cardVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Quiz Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-6 sm:py-8 text-white">
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="p-2 sm:p-3 bg-white/20 rounded-xl">
                <TbBrain className="w-6 h-6 sm:w-8 sm:h-8" />
              </div>
              <div className="min-w-0 flex-1">
                <h2 className="text-lg sm:text-2xl font-bold truncate">{quiz?.exam?.name}</h2>
                <p className="text-blue-100 text-sm sm:text-base">
                  {quiz?.exam?.subject} • {trialUserInfo.level.charAt(0).toUpperCase() + trialUserInfo.level.slice(1)} Level
                </p>
              </div>
            </div>
          </div>

          {/* Quiz Details */}
          <div className="p-4 sm:p-6">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
              <div className="text-center p-4 bg-blue-50 rounded-xl">
                <TbQuestionMark className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-800">
                  {quiz?.trialInfo?.trialQuestionCount || quiz?.exam?.questions?.length || 0}
                </div>
                <div className="text-sm text-gray-600">Questions</div>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-xl">
                <TbClock className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-800">
                  {quiz?.trialInfo?.trialDuration || quiz?.exam?.duration || 0}
                </div>
                <div className="text-sm text-gray-600">Minutes</div>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-xl">
                <TbBrain className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-800">
                  {quiz?.exam?.passingMarks || Math.ceil((quiz?.exam?.questions?.length || 0) * 0.6)}
                </div>
                <div className="text-sm text-gray-600">Pass Mark</div>
              </div>
            </div>

            {/* Trial Info */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-4 sm:p-6 mb-6">
              <h3 className="font-semibold text-blue-800 mb-3 flex items-center">
                <span className="text-lg mr-2">🎯</span>
                Personalized Trial Experience
              </h3>
              <ul className="text-sm text-blue-700 space-y-2">
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>Quiz specifically selected for <strong>{trialUserInfo.level} {trialUserInfo.class}</strong> students</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>Limited to {quiz?.trialInfo?.trialQuestionCount || 5} questions for trial experience</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>Instant results with detailed explanations</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>No registration required - start immediately</span>
                </li>
              </ul>
            </div>



            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={onBack}
                className="flex-1 py-3 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                Go Back
              </button>
              <motion.button
                onClick={handleStartQuiz}
                className="flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span>Start Trial Quiz</span>
                <TbArrowRight className="w-5 h-5" />
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Footer Note */}
        <motion.div
          className="text-center mt-6 text-sm text-gray-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          After completing this trial, you'll be invited to register for full access to our platform
        </motion.div>
      </div>
    </motion.div>
  );
};

export default TrialQuizSelection;
