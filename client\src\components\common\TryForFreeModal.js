import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { TbX, Tb<PERSON><PERSON>, Tb<PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TbArrowRight } from "react-icons/tb";
import { message } from "antd";

const TryForFreeModal = ({ isOpen, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: "",
    level: "",
    class: ""
  });
  const [loading, setLoading] = useState(false);

  // Level and class configurations
  const levelOptions = [
    { value: "primary", label: "Primary", description: "Classes 1-7", icon: "🌱" },
    { value: "secondary", label: "Secondary", description: "Form 1-4", icon: "📚" },
    { value: "advance", label: "Advance", description: "Form 5-6", icon: "🎓" }
  ];

  const classOptions = {
    primary: ["1", "2", "3", "4", "5", "6", "7"],
    secondary: ["Form-1", "Form-2", "Form-3", "Form-4"],
    advance: ["Form-5", "Form-6"]
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      // Reset class when level changes
      ...(field === "level" && { class: "" })
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      message.error("Please enter your name");
      return;
    }
    
    if (!formData.level) {
      message.error("Please select your level");
      return;
    }
    
    if (!formData.class) {
      message.error("Please select your class");
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      message.error("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 50 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { 
        type: "spring", 
        damping: 25, 
        stiffness: 300 
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8, 
      y: 50,
      transition: { duration: 0.2 }
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        variants={overlayVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        {/* Backdrop */}
        <motion.div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={onClose}
        />
        
        {/* Modal */}
        <motion.div
          className="relative w-full max-w-md mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden"
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-4 sm:px-6 py-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="p-2 bg-white/20 rounded-lg">
                  <TbBrain className="w-5 h-5 sm:w-6 sm:h-6" />
                </div>
                <div>
                  <h2 className="text-lg sm:text-xl font-bold">Try BrainWave Free</h2>
                  <p className="text-blue-100 text-xs sm:text-sm">Experience our platform with a sample quiz</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <TbX className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-4 sm:space-y-6">
            {/* Name Input */}
            <div className="space-y-2">
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700">
                <TbUser className="w-4 h-4" />
                <span>Your Name</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter your full name"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                maxLength={50}
              />
            </div>

            {/* Level Selection */}
            <div className="space-y-3">
              <label className="flex items-center space-x-2 text-sm font-medium text-gray-700">
                <TbSchool className="w-4 h-4" />
                <span>Education Level</span>
              </label>
              <div className="grid gap-3">
                {levelOptions.map((level) => (
                  <motion.button
                    key={level.value}
                    type="button"
                    onClick={() => handleInputChange("level", level.value)}
                    className={`p-4 border-2 rounded-lg text-left transition-all ${
                      formData.level === level.value
                        ? "border-blue-500 bg-blue-50 text-blue-700"
                        : "border-gray-200 hover:border-blue-300 hover:bg-blue-50"
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{level.icon}</span>
                      <div>
                        <div className="font-medium">{level.label}</div>
                        <div className="text-sm text-gray-500">{level.description}</div>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Class Selection */}
            {formData.level && (
              <motion.div
                className="space-y-3"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                transition={{ duration: 0.3 }}
              >
                <label className="text-sm font-medium text-gray-700">
                  Select Your Class
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {classOptions[formData.level]?.map((classOption) => (
                    <motion.button
                      key={classOption}
                      type="button"
                      onClick={() => handleInputChange("class", classOption)}
                      className={`p-2 sm:p-3 border-2 rounded-lg text-center font-medium transition-all text-sm sm:text-base ${
                        formData.class === classOption
                          ? "border-blue-500 bg-blue-500 text-white"
                          : "border-gray-200 hover:border-blue-300 hover:bg-blue-50"
                      }`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {classOption}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Submit Button */}
            <motion.button
              type="submit"
              disabled={loading || !formData.name || !formData.level || !formData.class}
              className={`w-full py-4 px-6 rounded-lg font-medium flex items-center justify-center space-x-2 transition-all ${
                loading || !formData.name || !formData.level || !formData.class
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl"
              }`}
              whileHover={!loading && formData.name && formData.level && formData.class ? { scale: 1.02 } : {}}
              whileTap={!loading && formData.name && formData.level && formData.class ? { scale: 0.98 } : {}}
            >
              {loading ? (
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                <>
                  <span>Start Free Trial</span>
                  <TbArrowRight className="w-5 h-5" />
                </>
              )}
            </motion.button>

            {/* Info Text */}
            <div className="text-center text-sm text-gray-500">
              <p>No registration required for trial</p>
              <p className="mt-1">Experience one quiz to see what BrainWave offers</p>
            </div>
          </form>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default TryForFreeModal;
