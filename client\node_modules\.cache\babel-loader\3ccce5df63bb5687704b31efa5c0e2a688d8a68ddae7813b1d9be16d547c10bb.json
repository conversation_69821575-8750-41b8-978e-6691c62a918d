{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbSchool, TbRocket } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport NotificationBell from \"../../../components/common/NotificationBell\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const navigate = useNavigate();\n  console.log(\"Current user state:\", user);\n\n  // Handle Try for Free modal\n  const handleTryForFree = () => {\n    console.log(\"Try for Free button clicked!\");\n    alert(\"Try for Free button clicked!\");\n    setShowTryForFreeModal(true);\n  };\n  const handleTryForFreeSubmit = trialData => {\n    console.log(\"Trial data submitted:\", trialData);\n    // Navigate to trial experience with user data\n    navigate('/trial', {\n      state: {\n        trialUserInfo: trialData\n      }\n    });\n    setShowTryForFreeModal(false);\n  };\n  const scrollToSection = (ref, offset = 80) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(reviewsSectionRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Reviews\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: '32px',\n                  height: '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: '32px',\n                  height: '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-2 sm:space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:flex items-center space-x-4 lg:space-x-6\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => scrollToSection(contactUsRef),\n                className: \"nav-item text-sm md:text-base\",\n                children: \"Contact Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), user && !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                  children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: homeSectionRef,\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-grid\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"hero-content\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"hero-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs sm:text-sm\",\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"hero-title\",\n              children: [\"Fueling Bright Futures with\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gradient\",\n                children: [\"Education\", /*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                  className: \"inline w-8 h-8 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"hero-subtitle\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"cta-section\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row items-center justify-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleTryForFree,\n                  className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium\",\n                  style: {\n                    zIndex: 10\n                  },\n                  children: \"\\uD83D\\uDE80 Try for Free\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary btn-large\",\n                    children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                      className: \"w-5 h-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 23\n                    }, this), \"Explore Platform\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), !user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.5\n              },\n              className: \"flex flex-col sm:flex-row items-center justify-center gap-3 mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"w-full sm:w-auto px-6 py-3 text-base font-medium text-blue-600 bg-white border-2 border-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-md hover:shadow-lg text-center\",\n                children: \"Login to Your Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"w-full sm:w-auto px-6 py-3 text-base font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg text-center\",\n                children: \"Create New Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"trust-indicators\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"hero-image\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: Image1,\n                alt: \"Students Learning\",\n                loading: \"lazy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  top: '-1rem',\n                  left: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  bottom: '-1rem',\n                  right: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 sm:py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\",\n          children: [{\n            number: \"15K+\",\n            text: \"Active Students\",\n            icon: TbUsers,\n            color: \"from-blue-500 to-blue-600\"\n          }, {\n            number: \"500+\",\n            text: \"Expert Teachers\",\n            icon: TbSchool,\n            color: \"from-green-500 to-green-600\"\n          }, {\n            number: \"1000+\",\n            text: \"Video Lessons\",\n            icon: TbBook,\n            color: \"from-purple-500 to-purple-600\"\n          }, {\n            number: \"98%\",\n            text: \"Success Rate\",\n            icon: TbTrophy,\n            color: \"from-orange-500 to-orange-600\"\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30,\n              scale: 0.9\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            whileHover: {\n              scale: 1.05,\n              y: -5\n            },\n            className: \"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n              children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs sm:text-sm md:text-base text-gray-600 font-medium\",\n              children: stat.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"reviews-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"reviews-title\",\n          children: \"Reviews from our students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviews-grid\",\n          children: [{\n            rating: 5,\n            text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\n            user: {\n              name: \"Sarah Johnson\"\n            }\n          }, {\n            rating: 5,\n            text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\n            user: {\n              name: \"Michael Chen\"\n            }\n          }, {\n            rating: 5,\n            text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\n            user: {\n              name: \"Amina Hassan\"\n            }\n          }].map((review, index) => {\n            var _review$user;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"review-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-rating\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#f59e0b',\n                    fontSize: '1.25rem'\n                  },\n                  children: '★'.repeat(review.rating)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-text\",\n                children: [\"\\\"\", review.text, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-divider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-author\",\n                children: (_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-blue-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h2, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-3xl sm:text-4xl font-bold text-gray-800 mb-4\",\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n            children: \"Get in touch with us for any questions or support. We're here to help you succeed!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"bg-white rounded-2xl shadow-xl p-6 sm:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-800 mb-6\",\n              children: \"Send us a Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSubmit,\n              className: \"space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  placeholder: \"Your Full Name\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  placeholder: \"<EMAIL>\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"message\",\n                  placeholder: \"Tell us how we can help you...\",\n                  rows: \"5\",\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\",\n                  value: formData.message,\n                  onChange: handleChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: loading ? \"Sending...\" : \"Send Message\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-center text-green-600 font-medium\",\n                children: responseMessage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 30\n            },\n            whileInView: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            viewport: {\n              once: true\n            },\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-6 sm:p-8 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Chat with us on WhatsApp\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-green-100 mb-6\",\n                children: \"Get instant support and quick answers to your questions through WhatsApp.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"https://wa.me/25565528549?text=Hello! I'm interested in learning more about BrainWave Educational Platform.\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"inline-flex items-center space-x-3 bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-green-50 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Chat on WhatsApp\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-green-100 text-sm mt-4\",\n                children: \"Phone: +255 655 285 49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-xl p-6 sm:p-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold text-gray-800 mb-6\",\n                children: \"Other Ways to Reach Us\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-blue-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 728,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Email Support\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"<EMAIL>\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-green-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 739,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Response Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"Usually within 2 hours\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-6 h-6 text-purple-600\",\n                      fill: \"none\",\n                      stroke: \"currentColor\",\n                      viewBox: \"0 0 24 24\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 750,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 751,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 749,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-medium text-gray-800\",\n                      children: \"Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 755,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"Dar es Salaam, Tanzania\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-text\",\n          children: \"\\xA9 2024 BrainWave Educational Platform. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 768,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TryForFreeModal, {\n      isOpen: showTryForFreeModal,\n      onClose: () => setShowTryForFreeModal(false),\n      onSubmit: handleTryForFreeSubmit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"2+OooZ71GBUhaFkx9H6QmV/qdRg=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "useLocation", "useNavigate", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbSchool", "TbRocket", "message", "useSelector", "Image1", "contactUs", "NotificationBell", "ProfilePicture", "TryForFreeModal", "jsxDEV", "_jsxDEV", "Home", "_s", "homeSectionRef", "reviewsSectionRef", "contactUsRef", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "showTryForFreeModal", "setShowTryForFreeModal", "user", "state", "navigate", "console", "log", "handleTryForFree", "alert", "handleTryForFreeSubmit", "trialData", "trialUserInfo", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "header", "initial", "y", "opacity", "animate", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "isAdmin", "size", "showOnlineStatus", "class", "zIndex", "to", "left", "bottom", "right", "whileInView", "viewport", "once", "number", "text", "icon", "map", "stat", "index", "rating", "review", "_review$user", "h2", "p", "onSubmit", "type", "placeholder", "onChange", "required", "rows", "disabled", "href", "rel", "fill", "viewBox", "d", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool,\r\n  TbRocket\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport NotificationBell from \"../../../components/common/NotificationBell\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport TryForFreeModal from \"../../../components/common/TryForFreeModal\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const [showTryForFreeModal, setShowTryForFreeModal] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const navigate = useNavigate();\r\n\r\n  console.log(\"Current user state:\", user);\r\n\r\n  // Handle Try for Free modal\r\n  const handleTryForFree = () => {\r\n    console.log(\"Try for Free button clicked!\");\r\n    alert(\"Try for Free button clicked!\");\r\n    setShowTryForFreeModal(true);\r\n  };\r\n\r\n  const handleTryForFreeSubmit = (trialData) => {\r\n    console.log(\"Trial data submitted:\", trialData);\r\n    // Navigate to trial experience with user data\r\n    navigate('/trial', { state: { trialUserInfo: trialData } });\r\n    setShowTryForFreeModal(false);\r\n  };\r\n\r\n\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      {/* Modern Responsive Header - Same as ProtectedRoute */}\r\n      <motion.header\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n      >\r\n        <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n          <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n            {/* Left section - Reviews */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item text-sm md:text-base\">Reviews</button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"relative group flex items-center space-x-3\"\r\n              >\r\n                {/* Tanzania Flag - Using actual flag image */}\r\n                <div\r\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                  style={{\r\n                    width: '32px',\r\n                    height: '24px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"https://flagcdn.com/w40/tz.png\"\r\n                    alt=\"Tanzania Flag\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      // Fallback to another flag source if first fails\r\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                      e.target.onerror = () => {\r\n                        // Final fallback - hide image and show text\r\n                        e.target.style.display = 'none';\r\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                      };\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Amazing Animated Brainwave Text */}\r\n                <div className=\"relative brainwave-container\">\r\n                  <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                      style={{\r\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                        letterSpacing: '-0.02em'\r\n                      }}>\r\n                    {/* Brain - with amazing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.3,\r\n                        textShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, -2, 2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#1f2937',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                      }}\r\n                    >\r\n                      Brain\r\n\r\n                      {/* Electric spark */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1.2, 0.5],\r\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                        }}\r\n                        transition={{\r\n                          duration: 1.5,\r\n                          repeat: Infinity,\r\n                          delay: 2\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#3b82f6',\r\n                          boxShadow: '0 0 10px #3b82f6'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n\r\n                    {/* Wave - with flowing effects (no space) */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        y: [0, -2, 0, 2, 0],\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.5,\r\n                        y: {\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        },\r\n                        textShadow: {\r\n                          duration: 2.5,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, 2, -2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#059669',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      }}\r\n                    >\r\n                      wave\r\n\r\n                      {/* Wave particle */}\r\n                      <motion.div\r\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          x: [0, 40, 80],\r\n                          y: [0, -5, 0, 5, 0],\r\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                        }}\r\n                        transition={{\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          delay: 1\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#10b981',\r\n                          boxShadow: '0 0 8px #10b981'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n                  </h1>\r\n\r\n                  {/* Glowing underline effect */}\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                    initial={{ width: 0, opacity: 0 }}\r\n                    animate={{\r\n                      width: '100%',\r\n                      opacity: 1,\r\n                      boxShadow: [\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.5,\r\n                      delay: 1.2,\r\n                      boxShadow: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Official Logo - Small like profile */}\r\n                <div\r\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                  style={{\r\n                    background: '#f0f0f0',\r\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                    width: '32px',\r\n                    height: '32px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div\r\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                    style={{\r\n                      display: 'none',\r\n                      fontSize: '12px'\r\n                    }}\r\n                  >\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Glow Effect */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Right Section - Contact Us + Notifications + User Profile */}\r\n            <div className=\"flex items-center justify-end space-x-2 sm:space-x-3\">\r\n              {/* Contact Us Button */}\r\n              <div className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item text-sm md:text-base\">Contact Us</button>\r\n              </div>\r\n\r\n              {/* Notification Bell */}\r\n              {user && !user?.isAdmin && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.2 }}\r\n                >\r\n                  <NotificationBell />\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* User Profile Section */}\r\n              {user && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={user}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.header>\r\n\r\n      {/* Hero Section */}\r\n      <section ref={homeSectionRef} className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-grid\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"hero-content\"\r\n            >\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"hero-badge\"\r\n              >\r\n                <TbSchool className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2\" />\r\n                <span className=\"text-xs sm:text-sm\">#1 Educational Platform in Tanzania</span>\r\n              </motion.div>\r\n\r\n              <h1 className=\"hero-title\">\r\n                Fueling Bright Futures with{\" \"}\r\n                <span className=\"text-gradient\">\r\n                  Education\r\n                  <TbArrowBigRightLinesFilled className=\"inline w-8 h-8 ml-2\" />\r\n                </span>\r\n              </h1>\r\n\r\n              <p className=\"hero-subtitle\">\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </p>\r\n\r\n              {/* CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"cta-section\"\r\n              >\r\n                <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4\">\r\n                  {/* Try for Free Button - Simplified for testing */}\r\n                  <button\r\n                    onClick={handleTryForFree}\r\n                    className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium\"\r\n                    style={{ zIndex: 10 }}\r\n                  >\r\n                    🚀 Try for Free\r\n                  </button>\r\n\r\n                  {/* Explore Platform Button */}\r\n                  <Link to=\"/login\">\r\n                    <button className=\"btn btn-primary btn-large\">\r\n                      <TbBrain className=\"w-5 h-5 mr-2\" />\r\n                      Explore Platform\r\n                    </button>\r\n                  </Link>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Login/Register buttons for non-logged in users */}\r\n              {!user && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.5 }}\r\n                  className=\"flex flex-col sm:flex-row items-center justify-center gap-3 mt-6\"\r\n                >\r\n                  <Link\r\n                    to=\"/login\"\r\n                    className=\"w-full sm:w-auto px-6 py-3 text-base font-medium text-blue-600 bg-white border-2 border-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-md hover:shadow-lg text-center\"\r\n                  >\r\n                    Login to Your Account\r\n                  </Link>\r\n                  <Link\r\n                    to=\"/register\"\r\n                    className=\"w-full sm:w-auto px-6 py-3 text-base font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg text-center\"\r\n                  >\r\n                    Create New Account\r\n                  </Link>\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"trust-indicators\"\r\n              >\r\n                <div className=\"trust-indicator\">\r\n                  <TbUsers style={{color: '#007BFF'}} />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbStar style={{color: '#f59e0b'}} />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbTrophy style={{color: '#007BFF'}} />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"hero-image\"\r\n            >\r\n              <div className=\"relative\">\r\n                <img\r\n                  src={Image1}\r\n                  alt=\"Students Learning\"\r\n                  loading=\"lazy\"\r\n                />\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{top: '-1rem', left: '-1rem'}}\r\n                >\r\n                  <TbBook style={{color: '#007BFF'}} />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{bottom: '-1rem', right: '-1rem'}}\r\n                >\r\n                  <TbTrophy style={{color: '#f59e0b'}} />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"py-16 sm:py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\", icon: TbUsers, color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", text: \"Expert Teachers\", icon: TbSchool, color: \"from-green-500 to-green-600\" },\r\n              { number: \"1000+\", text: \"Video Lessons\", icon: TbBook, color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"98%\", text: \"Success Rate\", icon: TbTrophy, color: \"from-orange-500 to-orange-600\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\r\n                whileInView={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                whileHover={{ scale: 1.05, y: -5 }}\r\n                className=\"bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100\"\r\n              >\r\n                <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full bg-gradient-to-r ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\r\n                  <stat.icon className=\"w-6 h-6 sm:w-8 sm:h-8 text-white\" />\r\n                </div>\r\n                <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-800 mb-1 sm:mb-2\">{stat.number}</div>\r\n                <div className=\"text-xs sm:text-sm md:text-base text-gray-600 font-medium\">{stat.text}</div>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"reviews-section\">\r\n        <div className=\"reviews-container\">\r\n          <h2 className=\"reviews-title\">\r\n            Reviews from our students\r\n          </h2>\r\n          <div className=\"reviews-grid\">\r\n            {[\r\n              {\r\n                rating: 5,\r\n                text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\r\n                user: { name: \"Sarah Johnson\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\r\n                user: { name: \"Michael Chen\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\r\n                user: { name: \"Amina Hassan\" }\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"review-card\"\r\n              >\r\n                <div className=\"review-rating\">\r\n                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>\r\n                    {'★'.repeat(review.rating)}\r\n                  </div>\r\n                </div>\r\n                <div className=\"review-text\">\"{review.text}\"</div>\r\n                <div className=\"review-divider\"></div>\r\n                <div className=\"review-author\">{review.user?.name}</div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-12\">\r\n            <motion.h2\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-3xl sm:text-4xl font-bold text-gray-800 mb-4\"\r\n            >\r\n              Contact Us\r\n            </motion.h2>\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-lg text-gray-600 max-w-2xl mx-auto\"\r\n            >\r\n              Get in touch with us for any questions or support. We're here to help you succeed!\r\n            </motion.p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\r\n            {/* Contact Form */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -30 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-2xl shadow-xl p-6 sm:p-8\"\r\n            >\r\n              <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">Send us a Message</h3>\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Name</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    placeholder=\"Your Full Name\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\r\n                    value={formData.name}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Email</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    placeholder=\"<EMAIL>\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all\"\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Message</label>\r\n                  <textarea\r\n                    name=\"message\"\r\n                    placeholder=\"Tell us how we can help you...\"\r\n                    rows=\"5\"\r\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none\"\r\n                    value={formData.message}\r\n                    onChange={handleChange}\r\n                    required\r\n                  ></textarea>\r\n                </div>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={loading}\r\n                  className=\"w-full py-3 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  {loading ? \"Sending...\" : \"Send Message\"}\r\n                </button>\r\n                {responseMessage && (\r\n                  <p className=\"text-center text-green-600 font-medium\">\r\n                    {responseMessage}\r\n                  </p>\r\n                )}\r\n              </form>\r\n            </motion.div>\r\n\r\n            {/* Contact Information & WhatsApp */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 30 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"space-y-8\"\r\n            >\r\n              {/* WhatsApp Contact */}\r\n              <div className=\"bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-6 sm:p-8 text-white\">\r\n                <h3 className=\"text-2xl font-bold mb-4\">Chat with us on WhatsApp</h3>\r\n                <p className=\"text-green-100 mb-6\">\r\n                  Get instant support and quick answers to your questions through WhatsApp.\r\n                </p>\r\n                <a\r\n                  href=\"https://wa.me/25565528549?text=Hello! I'm interested in learning more about BrainWave Educational Platform.\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"inline-flex items-center space-x-3 bg-white text-green-600 px-6 py-3 rounded-lg font-medium hover:bg-green-50 transition-all duration-300 shadow-lg hover:shadow-xl\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.346\"/>\r\n                  </svg>\r\n                  <span>Chat on WhatsApp</span>\r\n                </a>\r\n                <p className=\"text-green-100 text-sm mt-4\">\r\n                  Phone: +255 655 285 49\r\n                </p>\r\n              </div>\r\n\r\n              {/* Other Contact Methods */}\r\n              <div className=\"bg-white rounded-2xl shadow-xl p-6 sm:p-8\">\r\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">Other Ways to Reach Us</h3>\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Email Support</p>\r\n                      <p className=\"text-gray-600\"><EMAIL></p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Response Time</p>\r\n                      <p className=\"text-gray-600\">Usually within 2 hours</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\r\n                      <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\r\n                      </svg>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"font-medium text-gray-800\">Location</p>\r\n                      <p className=\"text-gray-600\">Dar es Salaam, Tanzania</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"footer-content\">\r\n          <p className=\"footer-text\">\r\n            © 2024 BrainWave Educational Platform. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n\r\n      {/* Try for Free Modal */}\r\n      <TryForFreeModal\r\n        isOpen={showTryForFreeModal}\r\n        onClose={() => setShowTryForFreeModal(false)}\r\n        onSubmit={handleTryForFreeSubmit}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,QAAQ,QACH,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,eAAe,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAGxB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyB,iBAAiB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM0B,YAAY,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC;IAAE8B,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEjB,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM;IAAEsC;EAAK,CAAC,GAAGvB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAE9BqC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEJ,IAAI,CAAC;;EAExC;EACA,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CE,KAAK,CAAC,8BAA8B,CAAC;IACrCP,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMQ,sBAAsB,GAAIC,SAAS,IAAK;IAC5CL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,SAAS,CAAC;IAC/C;IACAN,QAAQ,CAAC,QAAQ,EAAE;MAAED,KAAK,EAAE;QAAEQ,aAAa,EAAED;MAAU;IAAE,CAAC,CAAC;IAC3DT,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAID,MAAMW,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE7B,IAAI;MAAE8B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChChC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAG8B;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB9B,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAM6B,IAAI,GAAG,MAAM/C,SAAS,CAACW,QAAQ,CAAC;MACtC,IAAIoC,IAAI,CAACC,OAAO,EAAE;QAChBnD,OAAO,CAACmD,OAAO,CAAC,4BAA4B,CAAC;QAC7C9B,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEjB,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLqB,kBAAkB,CAAC6B,IAAI,CAAClD,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOoD,KAAK,EAAE;MACd/B,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEX,OAAA;IAAK6C,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEnB9C,OAAA,CAACjB,MAAM,CAACgE,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5K9C,OAAA;QAAK6C,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5D9C,OAAA;UAAK6C,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBAErF9C,OAAA;YAAK6C,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1C9C,OAAA;cAAK6C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE9C,OAAA;gBAAQoD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACtB,iBAAiB,CAAE;gBAACyC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxD,OAAA;YAAK6C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC9C,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAGtD9C,OAAA;gBACE6C,SAAS,EAAC,wEAAwE;gBAClFiB,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEF9C,OAAA;kBACEiE,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACE,MAAM,CAAC0B,GAAG,GAAG,8GAA8G;oBAC7H5B,CAAC,CAACE,MAAM,CAAC8B,OAAO,GAAG,MAAM;sBACvB;sBACAhC,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BjC,CAAC,CAACE,MAAM,CAACgC,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxD,OAAA;gBAAK6C,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C9C,OAAA;kBAAI6C,SAAS,EAAC,qFAAqF;kBAC/FiB,KAAK,EAAE;oBACLW,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA5B,QAAA,gBAEJ9C,OAAA,CAACjB,MAAM,CAAC4F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,CAAC,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC5CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVgB,UAAU,EAAE;wBACVjB,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,OAGC,eACA9C,OAAA,CAACjB,MAAM,CAAC0E,GAAG;sBACTZ,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBQ,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtB2B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdxD,OAAA,CAACjB,MAAM,CAAC4F,IAAI;oBACV9B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAE0B,CAAC,EAAE,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC3CP,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACV0B,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRT,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB4B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVZ,CAAC,EAAE;wBACDW,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVjB,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA/B,QAAA,GACH,MAGC,eACA9C,OAAA,CAACjB,MAAM,CAAC0E,GAAG;sBACTZ,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClB0B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd3B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBoC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLxD,OAAA,CAACjB,MAAM,CAAC0E,GAAG;kBACTZ,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEe,KAAK,EAAE,CAAC;oBAAEb,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPY,KAAK,EAAE,MAAM;oBACbb,OAAO,EAAE,CAAC;oBACVoC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACF3B,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVyB,SAAS,EAAE;sBACT1B,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFlB,KAAK,EAAE;oBACLyB,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNxD,OAAA;gBACE6C,SAAS,EAAC,gEAAgE;gBAC1EiB,KAAK,EAAE;kBACLyB,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvCvB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,gBAEF9C,OAAA;kBACEiE,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBrB,SAAS,EAAC,4BAA4B;kBACtCiB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG/B,CAAC,IAAK;oBACdA,CAAC,CAACE,MAAM,CAACuB,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC/BjC,CAAC,CAACE,MAAM,CAACiD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFxD,OAAA;kBACE6C,SAAS,EAAC,gHAAgH;kBAC1HiB,KAAK,EAAE;oBACLQ,OAAO,EAAE,MAAM;oBACfmB,QAAQ,EAAE;kBACZ,CAAE;kBAAA3C,QAAA,EACH;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxD,OAAA;gBAAK6C,SAAS,EAAC;cAAyK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNxD,OAAA;YAAK6C,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBAEnE9C,OAAA;cAAK6C,SAAS,EAAC,oDAAoD;cAAAC,QAAA,eACjE9C,OAAA;gBAAQoD,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACrB,YAAY,CAAE;gBAACwC,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAU;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChH,CAAC,EAGLxC,IAAI,IAAI,EAACA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0E,OAAO,kBACrB1F,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAAf,QAAA,eAE1C9C,OAAA,CAACJ,gBAAgB;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACb,EAGAxC,IAAI,iBACHhB,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAI,CAAE;cACpCP,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEQ,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAG7C9C,OAAA,CAACH,cAAc;gBACbmB,IAAI,EAAEA,IAAK;gBACX2E,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvB9B,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFxD,OAAA;gBAAK6C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC9C,OAAA;kBAAK6C,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,EACpH,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,KAAI;gBAAM;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNxD,OAAA;kBAAK6C,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,GAAC,QACzF,EAAC9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,KAAK;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGhBxD,OAAA;MAAS2B,GAAG,EAAExB,cAAe;MAAC0C,SAAS,EAAC,cAAc;MAAAC,QAAA,eACpD9C,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB9C,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB9C,OAAA,CAACjB,MAAM,CAAC0E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCzB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bf,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExB9C,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAEtB9C,OAAA,CAACV,QAAQ;gBAACuD,SAAS,EAAC;cAA4B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDxD,OAAA;gBAAM6C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAEbxD,OAAA;cAAI6C,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,6BACE,EAAC,GAAG,eAC/B9C,OAAA;gBAAM6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,WAE9B,eAAA9C,OAAA,CAAChB,0BAA0B;kBAAC6D,SAAS,EAAC;gBAAqB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAELxD,OAAA;cAAG6C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJxD,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,aAAa;cAAAC,QAAA,eAEvB9C,OAAA;gBAAK6C,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAE1E9C,OAAA;kBACEoD,OAAO,EAAE/B,gBAAiB;kBAC1BwB,SAAS,EAAC,+FAA+F;kBACzGiB,KAAK,EAAE;oBAAEgC,MAAM,EAAE;kBAAG,CAAE;kBAAAhD,QAAA,EACvB;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAGTxD,OAAA,CAACpB,IAAI;kBAACmH,EAAE,EAAC,QAAQ;kBAAAjD,QAAA,eACf9C,OAAA;oBAAQ6C,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBAC3C9C,OAAA,CAACf,OAAO;sBAAC4D,SAAS,EAAC;oBAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,oBAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGZ,CAACxC,IAAI,iBACJhB,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAE5E9C,OAAA,CAACpB,IAAI;gBACHmH,EAAE,EAAC,QAAQ;gBACXlD,SAAS,EAAC,gMAAgM;gBAAAC,QAAA,EAC3M;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPxD,OAAA,CAACpB,IAAI;gBACHmH,EAAE,EAAC,WAAW;gBACdlD,SAAS,EAAC,wKAAwK;gBAAAC,QAAA,EACnL;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CACb,eAGDxD,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cACTT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1ChB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE5B9C,OAAA;gBAAK6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B9C,OAAA,CAACZ,OAAO;kBAAC0E,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtCxD,OAAA;kBAAA8C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNxD,OAAA;gBAAK6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B9C,OAAA,CAACX,MAAM;kBAACyE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCxD,OAAA;kBAAA8C,QAAA,EAAM;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNxD,OAAA;gBAAK6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B9C,OAAA,CAACb,QAAQ;kBAAC2E,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCxD,OAAA;kBAAA8C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbxD,OAAA,CAACjB,MAAM,CAAC0E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAG,CAAE;YAC/BzB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,YAAY;YAAAC,QAAA,eAEtB9C,OAAA;cAAK6C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB9C,OAAA;gBACEiE,GAAG,EAAEvE,MAAO;gBACZwE,GAAG,EAAC,mBAAmB;gBACvBxD,OAAO,EAAC;cAAM;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGFxD,OAAA,CAACjB,MAAM,CAAC0E,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC;gBAAS,CAAE;gBAC9ClC,SAAS,EAAC,kBAAkB;gBAC5BiB,KAAK,EAAE;kBAAC5B,GAAG,EAAE,OAAO;kBAAE8D,IAAI,EAAE;gBAAO,CAAE;gBAAAlD,QAAA,eAErC9C,OAAA,CAACd,MAAM;kBAAC4E,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEbxD,OAAA,CAACjB,MAAM,CAAC0E,GAAG;gBACTN,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BU,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC;gBAAS,CAAE;gBAC9ClC,SAAS,EAAC,kBAAkB;gBAC5BiB,KAAK,EAAE;kBAACmC,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAApD,QAAA,eAEzC9C,OAAA,CAACb,QAAQ;kBAAC2E,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxD,OAAA;MAAS6C,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eACvF9C,OAAA;QAAK6C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD9C,OAAA,CAACjB,MAAM,CAAC0E,GAAG;UACTT,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BkD,WAAW,EAAE;YAAEjD,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCU,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BwC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxD,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAEzD,CACC;YAAEwD,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAEpH,OAAO;YAAE+F,KAAK,EAAE;UAA4B,CAAC,EAC9F;YAAEmB,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE,iBAAiB;YAAEC,IAAI,EAAElH,QAAQ;YAAE6F,KAAK,EAAE;UAA8B,CAAC,EACjG;YAAEmB,MAAM,EAAE,OAAO;YAAEC,IAAI,EAAE,eAAe;YAAEC,IAAI,EAAEtH,MAAM;YAAEiG,KAAK,EAAE;UAAgC,CAAC,EAChG;YAAEmB,MAAM,EAAE,KAAK;YAAEC,IAAI,EAAE,cAAc;YAAEC,IAAI,EAAErH,QAAQ;YAAEgG,KAAK,EAAE;UAAgC,CAAC,CAChG,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChB3G,OAAA,CAACjB,MAAM,CAAC0E,GAAG;YAETT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE,EAAE;cAAES,KAAK,EAAE;YAAI,CAAE;YAC3CyC,WAAW,EAAE;cAAEjD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE,CAAC;cAAES,KAAK,EAAE;YAAE,CAAE;YAC5CC,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE8C,KAAK,GAAG;YAAI,CAAE;YAClDP,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBpB,UAAU,EAAE;cAAEvB,KAAK,EAAE,IAAI;cAAET,CAAC,EAAE,CAAC;YAAE,CAAE;YACnCJ,SAAS,EAAC,gIAAgI;YAAAC,QAAA,gBAE1I9C,OAAA;cAAK6C,SAAS,EAAG,gFAA+E6D,IAAI,CAACvB,KAAM,2FAA2F;cAAArC,QAAA,eACpM9C,OAAA,CAAC0G,IAAI,CAACF,IAAI;gBAAC3D,SAAS,EAAC;cAAkC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNxD,OAAA;cAAK6C,SAAS,EAAC,uEAAuE;cAAAC,QAAA,EAAE4D,IAAI,CAACJ;YAAM;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1GxD,OAAA;cAAK6C,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EAAE4D,IAAI,CAACH;YAAI;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAZvFmD,KAAK;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxD,OAAA;MAAS2B,GAAG,EAAEvB,iBAAkB;MAACyC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC1D9C,OAAA;QAAK6C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9C,OAAA;UAAI6C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxD,OAAA;UAAK6C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,CACC;YACE8D,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,yIAAyI;YAC/IvF,IAAI,EAAE;cAAER,IAAI,EAAE;YAAgB;UAChC,CAAC,EACD;YACEoG,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,uIAAuI;YAC7IvF,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,EACD;YACEoG,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,mHAAmH;YACzHvF,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,CACF,CAACiG,GAAG,CAAC,CAACI,MAAM,EAAEF,KAAK;YAAA,IAAAG,YAAA;YAAA,oBAClB9G,OAAA,CAACjB,MAAM,CAAC0E,GAAG;cAETT,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BkD,WAAW,EAAE;gBAAEjD,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAClCU,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE8C,KAAK,GAAG;cAAI,CAAE;cAClDP,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBxD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB9C,OAAA;gBAAK6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B9C,OAAA;kBAAK8D,KAAK,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEM,QAAQ,EAAE;kBAAU,CAAE;kBAAA3C,QAAA,EACnD,GAAG,CAACgC,MAAM,CAAC+B,MAAM,CAACD,MAAM;gBAAC;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxD,OAAA;gBAAK6C,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,IAAC,EAAC+D,MAAM,CAACN,IAAI,EAAC,IAAC;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClDxD,OAAA;gBAAK6C,SAAS,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCxD,OAAA;gBAAK6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAgE,YAAA,GAAED,MAAM,CAAC7F,IAAI,cAAA8F,YAAA,uBAAXA,YAAA,CAAatG;cAAI;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAdnDmD,KAAK;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxD,OAAA;MAAS2B,GAAG,EAAEtB,YAAa;MAACwC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eAC9F9C,OAAA;QAAK6C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD9C,OAAA;UAAK6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9C,OAAA,CAACjB,MAAM,CAACgI,EAAE;YACR/D,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkD,WAAW,EAAE;cAAEjD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BwC,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxD,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAC9D;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACZxD,OAAA,CAACjB,MAAM,CAACiI,CAAC;YACPhE,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BkD,WAAW,EAAE;cAAEjD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCU,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CuC,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxD,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EACpD;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENxD,OAAA;UAAK6C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD9C,OAAA,CAACjB,MAAM,CAAC0E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCuB,WAAW,EAAE;cAAEjD,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAClCjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BwC,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxD,SAAS,EAAC,2CAA2C;YAAAC,QAAA,gBAErD9C,OAAA;cAAI6C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAiB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5ExD,OAAA;cAAMiH,QAAQ,EAAEzE,YAAa;cAACK,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACjD9C,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAO6C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5ExD,OAAA;kBACEkH,IAAI,EAAC,MAAM;kBACX1G,IAAI,EAAC,MAAM;kBACX2G,WAAW,EAAC,gBAAgB;kBAC5BtE,SAAS,EAAC,6HAA6H;kBACvIP,KAAK,EAAEhC,QAAQ,CAACE,IAAK;kBACrB4G,QAAQ,EAAEhF,YAAa;kBACvBiF,QAAQ;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAO6C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7ExD,OAAA;kBACEkH,IAAI,EAAC,OAAO;kBACZ1G,IAAI,EAAC,OAAO;kBACZ2G,WAAW,EAAC,wBAAwB;kBACpCtE,SAAS,EAAC,6HAA6H;kBACvIP,KAAK,EAAEhC,QAAQ,CAACG,KAAM;kBACtB2G,QAAQ,EAAEhF,YAAa;kBACvBiF,QAAQ;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAO6C,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/ExD,OAAA;kBACEQ,IAAI,EAAC,SAAS;kBACd2G,WAAW,EAAC,gCAAgC;kBAC5CG,IAAI,EAAC,GAAG;kBACRzE,SAAS,EAAC,yIAAyI;kBACnJP,KAAK,EAAEhC,QAAQ,CAACd,OAAQ;kBACxB4H,QAAQ,EAAEhF,YAAa;kBACvBiF,QAAQ;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxD,OAAA;gBACEkH,IAAI,EAAC,QAAQ;gBACbK,QAAQ,EAAE7G,OAAQ;gBAClBmC,SAAS,EAAC,2OAA2O;gBAAAC,QAAA,EAEpPpC,OAAO,GAAG,YAAY,GAAG;cAAc;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACR5C,eAAe,iBACdZ,OAAA;gBAAG6C,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAClDlC;cAAe;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGbxD,OAAA,CAACjB,MAAM,CAAC0E,GAAG;YACTT,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAG,CAAE;YAC/BuB,WAAW,EAAE;cAAEjD,OAAO,EAAE,CAAC;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAClCjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BwC,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAGrB9C,OAAA;cAAK6C,SAAS,EAAC,gFAAgF;cAAAC,QAAA,gBAC7F9C,OAAA;gBAAI6C,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAwB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrExD,OAAA;gBAAG6C,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAEnC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJxD,OAAA;gBACEwH,IAAI,EAAC,6GAA6G;gBAClHjF,MAAM,EAAC,QAAQ;gBACfkF,GAAG,EAAC,qBAAqB;gBACzB5E,SAAS,EAAC,qKAAqK;gBAAAC,QAAA,gBAE/K9C,OAAA;kBAAK6C,SAAS,EAAC,SAAS;kBAAC6E,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAA7E,QAAA,eAC9D9C,OAAA;oBAAM4H,CAAC,EAAC;kBAAklC;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzlC,CAAC,eACNxD,OAAA;kBAAA8C,QAAA,EAAM;gBAAgB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACJxD,OAAA;gBAAG6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAE3C;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNxD,OAAA;cAAK6C,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxD9C,OAAA;gBAAI6C,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAsB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjFxD,OAAA;gBAAK6C,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9C,OAAA;kBAAK6C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9C,OAAA;oBAAK6C,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChF9C,OAAA;sBAAK6C,SAAS,EAAC,uBAAuB;sBAAC6E,IAAI,EAAC,MAAM;sBAACG,MAAM,EAAC,cAAc;sBAACF,OAAO,EAAC,WAAW;sBAAA7E,QAAA,eAC1F9C,OAAA;wBAAM8H,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACJ,CAAC,EAAC;sBAAsG;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1K;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNxD,OAAA;oBAAA8C,QAAA,gBACE9C,OAAA;sBAAG6C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DxD,OAAA;sBAAG6C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxD,OAAA;kBAAK6C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9C,OAAA;oBAAK6C,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjF9C,OAAA;sBAAK6C,SAAS,EAAC,wBAAwB;sBAAC6E,IAAI,EAAC,MAAM;sBAACG,MAAM,EAAC,cAAc;sBAACF,OAAO,EAAC,WAAW;sBAAA7E,QAAA,eAC3F9C,OAAA;wBAAM8H,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACJ,CAAC,EAAC;sBAA6C;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNxD,OAAA;oBAAA8C,QAAA,gBACE9C,OAAA;sBAAG6C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1DxD,OAAA;sBAAG6C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAsB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxD,OAAA;kBAAK6C,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C9C,OAAA;oBAAK6C,SAAS,EAAC,qEAAqE;oBAAAC,QAAA,eAClF9C,OAAA;sBAAK6C,SAAS,EAAC,yBAAyB;sBAAC6E,IAAI,EAAC,MAAM;sBAACG,MAAM,EAAC,cAAc;sBAACF,OAAO,EAAC,WAAW;sBAAA7E,QAAA,gBAC5F9C,OAAA;wBAAM8H,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACJ,CAAC,EAAC;sBAAoF;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAC3JxD,OAAA;wBAAM8H,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,WAAW,EAAC,GAAG;wBAACJ,CAAC,EAAC;sBAAkC;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNxD,OAAA;oBAAA8C,QAAA,gBACE9C,OAAA;sBAAG6C,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrDxD,OAAA;sBAAG6C,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAuB;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVxD,OAAA;MAAQ6C,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxB9C,OAAA;QAAK6C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B9C,OAAA;UAAG6C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTxD,OAAA,CAACF,eAAe;MACdmI,MAAM,EAAEnH,mBAAoB;MAC5BoH,OAAO,EAAEA,CAAA,KAAMnH,sBAAsB,CAAC,KAAK,CAAE;MAC7CkG,QAAQ,EAAE1F;IAAuB;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtD,EAAA,CAxvBID,IAAI;EAAA,QAQSR,WAAW,EACXX,WAAW;AAAA;AAAAqJ,EAAA,GATxBlI,IAAI;AA0vBV,eAAeA,IAAI;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}