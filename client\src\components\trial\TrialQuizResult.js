import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { 
  TbTrophy, 
  TbCheck, 
  TbX, 
  TbClock, 
  TbBrain, 
  TbArrowRight,
  TbStar,
  TbUsers,
  TbBook,
  TbMessageCircle,
  TbChartBar
} from "react-icons/tb";

const TrialQuizResult = ({ result, onTryAnother, onRegister }) => {
  const [showDetails, setShowDetails] = useState(false);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getPerformanceMessage = (percentage) => {
    if (percentage >= 90) return { message: "Outstanding! 🌟", color: "text-purple-600", bg: "bg-purple-50" };
    if (percentage >= 80) return { message: "Excellent! 🎉", color: "text-green-600", bg: "bg-green-50" };
    if (percentage >= 70) return { message: "Great Job! 👏", color: "text-blue-600", bg: "bg-blue-50" };
    if (percentage >= 60) return { message: "Well Done! ✨", color: "text-green-600", bg: "bg-green-50" };
    if (percentage >= 40) return { message: "Good Effort! 💪", color: "text-yellow-600", bg: "bg-yellow-50" };
    return { message: "Keep Practicing! 📚", color: "text-orange-600", bg: "bg-orange-50" };
  };

  const performance = getPerformanceMessage(result.percentage);

  const premiumFeatures = [
    {
      icon: TbBook,
      title: "Study Materials",
      description: "Access comprehensive study materials, notes, and resources"
    },
    {
      icon: TbBrain,
      title: "AI Assistant",
      description: "Get personalized explanations and study recommendations"
    },
    {
      icon: TbChartBar,
      title: "Ranking System",
      description: "Compete with other students and track your progress"
    },
    {
      icon: TbMessageCircle,
      title: "Forum Access",
      description: "Ask questions and help other students in our community"
    },
    {
      icon: TbUsers,
      title: "Unlimited Quizzes",
      description: "Take as many quizzes as you want across all subjects"
    },
    {
      icon: TbStar,
      title: "Progress Tracking",
      description: "Detailed analytics and performance insights"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Result Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${performance.bg} mb-4`}>
            <TbTrophy className={`w-10 h-10 ${performance.color}`} />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-2">
            Trial Complete!
          </h1>
          <p className={`text-xl font-semibold ${performance.color}`}>
            {performance.message}
          </p>
        </motion.div>

        {/* Score Card */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl p-8 mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div className="p-4 bg-blue-50 rounded-xl">
              <div className="text-3xl font-bold text-blue-600">{result.percentage}%</div>
              <div className="text-sm text-gray-600">Score</div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-xl">
              <div className="text-3xl font-bold text-green-600">{result.correctAnswers}</div>
              <div className="text-sm text-gray-600">Correct</div>
            </div>
            
            <div className="p-4 bg-red-50 rounded-xl">
              <div className="text-3xl font-bold text-red-600">{result.wrongAnswers}</div>
              <div className="text-sm text-gray-600">Wrong</div>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-xl">
              <div className="text-3xl font-bold text-purple-600">{formatTime(result.timeSpent)}</div>
              <div className="text-sm text-gray-600">Time</div>
            </div>
          </div>

          {/* Pass/Fail Status */}
          <div className="mt-6 text-center">
            <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${
              result.passed 
                ? 'bg-green-100 text-green-700' 
                : 'bg-red-100 text-red-700'
            }`}>
              {result.passed ? <TbCheck className="w-5 h-5" /> : <TbX className="w-5 h-5" />}
              <span className="font-medium">
                {result.passed ? 'Passed' : 'Not Passed'} • {result.examName}
              </span>
            </div>
          </div>

          {/* Show Details Button */}
          <div className="mt-6 text-center">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              {showDetails ? 'Hide Details' : 'Show Question Details'}
            </button>
          </div>
        </motion.div>

        {/* Question Details */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl shadow-xl p-6 mb-8"
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4">Question Review</h3>
            <div className="space-y-4">
              {result.questionResults?.map((q, index) => (
                <div key={index} className={`p-4 rounded-lg border-2 ${
                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                    }`}>
                      {q.isCorrect ? <TbCheck className="w-4 h-4" /> : <TbX className="w-4 h-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-800 mb-2">{q.question}</p>
                      <div className="text-sm space-y-1">
                        <p>
                          <span className="text-gray-600">Your answer:</span>
                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                            {q.userAnswer || 'Not answered'}
                          </span>
                        </p>
                        {!q.isCorrect && (
                          <p>
                            <span className="text-gray-600">Correct answer:</span>
                            <span className="ml-2 font-medium text-green-600">{q.correctAnswer}</span>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Premium Features Showcase */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl shadow-xl p-8 text-white mb-8"
        >
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold mb-2">
              Unlock Your Full Potential! 🚀
            </h2>
            <p className="text-blue-100 text-lg">
              Register now to access all premium features and accelerate your learning
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {premiumFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-4"
              >
                <feature.icon className="w-8 h-8 text-blue-200 mb-3" />
                <h3 className="font-semibold mb-2">{feature.title}</h3>
                <p className="text-sm text-blue-100">{feature.description}</p>
              </motion.div>
            ))}
          </div>

          <div className="text-center">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register">
                <motion.button
                  className="px-8 py-4 bg-white text-blue-600 rounded-xl font-bold text-lg hover:bg-blue-50 transition-all shadow-lg hover:shadow-xl flex items-center space-x-2"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>Register Now</span>
                  <TbArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>
              
              <Link to="/login">
                <button className="px-8 py-4 border-2 border-white text-white rounded-xl font-bold text-lg hover:bg-white/10 transition-all">
                  Already Have Account?
                </button>
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <button
            onClick={onTryAnother}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            Try Another Quiz
          </button>
          
          <Link to="/">
            <button className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium">
              Back to Home
            </button>
          </Link>
        </motion.div>

        {/* Trial Badge */}
        <div className="text-center mt-8">
          <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium">
            🎯 Trial Mode - Register for unlimited access
          </span>
        </div>
      </div>
    </div>
  );
};

export default TrialQuizResult;
