import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { 
  TbTrophy, 
  TbCheck, 
  TbX, 
  TbClock, 
  TbBrain, 
  TbArrowRight,
  TbStar,
  TbUsers,
  TbBook,
  TbMessageCircle,
  TbChartBar
} from "react-icons/tb";

const TrialQuizResult = ({ result, onTryAnother, onRegister }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getPerformanceMessage = (percentage) => {
    if (percentage >= 90) return {
      message: "Outstanding Performance! 🌟",
      color: "text-purple-600",
      bg: "bg-purple-50",
      gradient: "from-purple-500 to-purple-600"
    };
    if (percentage >= 80) return {
      message: "Excellent Work! 🎉",
      color: "text-green-600",
      bg: "bg-green-50",
      gradient: "from-green-500 to-green-600"
    };
    if (percentage >= 70) return {
      message: "Great Job! 👏",
      color: "text-blue-600",
      bg: "bg-blue-50",
      gradient: "from-blue-500 to-blue-600"
    };
    if (percentage >= 60) return {
      message: "Well Done! ✨",
      color: "text-emerald-600",
      bg: "bg-emerald-50",
      gradient: "from-emerald-500 to-emerald-600"
    };
    if (percentage >= 40) return {
      message: "Good Effort! 💪",
      color: "text-yellow-600",
      bg: "bg-yellow-50",
      gradient: "from-yellow-500 to-yellow-600"
    };
    return {
      message: "Keep Practicing! 📚",
      color: "text-orange-600",
      bg: "bg-orange-50",
      gradient: "from-orange-500 to-orange-600"
    };
  };

  const performance = getPerformanceMessage(result.percentage);
  const isPassed = result.percentage >= 60;

  const premiumFeatures = [
    {
      icon: TbBook,
      title: "Study Materials",
      description: "Access comprehensive study materials, notes, and resources"
    },
    {
      icon: TbBrain,
      title: "AI Assistant",
      description: "Get personalized explanations and study recommendations"
    },
    {
      icon: TbChartBar,
      title: "Ranking System",
      description: "Compete with other students and track your progress"
    },
    {
      icon: TbMessageCircle,
      title: "Forum Access",
      description: "Ask questions and help other students in our community"
    },
    {
      icon: TbUsers,
      title: "Unlimited Quizzes",
      description: "Take as many quizzes as you want across all subjects"
    },
    {
      icon: TbStar,
      title: "Progress Tracking",
      description: "Detailed analytics and performance insights"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 py-6 sm:py-12 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Animated Result Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center mb-8 sm:mb-12"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3, type: "spring", stiffness: 200 }}
            className={`inline-flex items-center justify-center w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full bg-gradient-to-r ${performance.gradient} mb-6 shadow-lg`}
            onAnimationComplete={() => setAnimationComplete(true)}
          >
            <TbTrophy className="w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 text-white" />
          </motion.div>

          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4"
          >
            Quiz Complete! 🎉
          </motion.h1>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className={`inline-block px-6 py-3 rounded-full ${performance.bg} border-2 border-${performance.color.split('-')[1]}-200`}
          >
            <p className={`text-xl sm:text-2xl font-bold ${performance.color}`}>
              {performance.message}
            </p>
          </motion.div>
        </motion.div>

        {/* Animated Score Card */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden mb-8 sm:mb-12"
        >
          {/* Score Header */}
          <div className={`bg-gradient-to-r ${performance.gradient} px-6 sm:px-8 lg:px-10 py-6 sm:py-8`}>
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8, delay: 1.2, type: "spring" }}
                className="text-6xl sm:text-7xl lg:text-8xl font-bold text-white mb-2"
              >
                {result.percentage}%
              </motion.div>
              <div className="text-white/90 text-lg sm:text-xl">
                Your Score
              </div>
            </div>
          </div>

          {/* Score Details */}
          <div className="px-6 sm:px-8 lg:px-10 py-8 sm:py-10"
        >
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
              {[
                {
                  label: "Total Questions",
                  value: result.totalQuestions,
                  icon: TbBook,
                  color: "blue",
                  delay: 1.4
                },
                {
                  label: "Correct Answers",
                  value: result.correctAnswers,
                  icon: TbCheck,
                  color: "green",
                  delay: 1.6
                },
                {
                  label: "Wrong Answers",
                  value: result.wrongAnswers,
                  icon: TbX,
                  color: "red",
                  delay: 1.8
                },
                {
                  label: "Time Taken",
                  value: formatTime(result.timeSpent),
                  icon: TbClock,
                  color: "purple",
                  delay: 2.0
                }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: stat.delay }}
                  className={`p-4 sm:p-6 bg-${stat.color}-50 rounded-2xl border border-${stat.color}-100 text-center`}
                >
                  <div className={`w-12 h-12 mx-auto mb-3 bg-${stat.color}-100 rounded-xl flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
                  </div>
                  <div className={`text-2xl sm:text-3xl font-bold text-${stat.color}-600 mb-1`}>
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Pass/Fail Status */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 2.2 }}
              className="text-center mb-8"
            >
              <div className={`inline-flex items-center space-x-3 px-6 py-4 rounded-2xl text-lg font-semibold ${
                isPassed
                  ? 'bg-green-100 text-green-700 border-2 border-green-200'
                  : 'bg-red-100 text-red-700 border-2 border-red-200'
              }`}>
                {isPassed ? (
                  <TbCheck className="w-6 h-6" />
                ) : (
                  <TbX className="w-6 h-6" />
                )}
                <span>
                  {isPassed ? '🎉 Congratulations! You Passed!' : '📚 Keep Studying! You Can Do Better!'}
                </span>
              </div>
            </motion.div>

            {/* Show Details Button */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 2.4 }}
              className="text-center"
            >
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="inline-flex items-center space-x-2 px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200"
              >
                <TbChartBar className="w-5 h-5" />
                <span>{showDetails ? 'Hide Question Summary' : 'View Question Summary'}</span>
              </button>
            </motion.div>
          </div>
        </motion.div>

        {/* Question Details */}
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl shadow-xl p-6 mb-8"
          >
            <h3 className="text-xl font-bold text-gray-800 mb-4">Question Review</h3>
            <div className="space-y-4">
              {result.questionResults?.map((q, index) => (
                <div key={index} className={`p-4 rounded-lg border-2 ${
                  q.isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      q.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                    }`}>
                      {q.isCorrect ? <TbCheck className="w-4 h-4" /> : <TbX className="w-4 h-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-800 mb-2">{q.question}</p>
                      <div className="text-sm space-y-1">
                        <p>
                          <span className="text-gray-600">Your answer:</span>
                          <span className={`ml-2 font-medium ${q.isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                            {q.userAnswer || 'Not answered'}
                          </span>
                        </p>
                        {!q.isCorrect && (
                          <p>
                            <span className="text-gray-600">Correct answer:</span>
                            <span className="ml-2 font-medium text-green-600">{q.correctAnswer}</span>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Premium Features Showcase */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 rounded-3xl shadow-2xl mb-8"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 to-transparent"></div>
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-white/10 rounded-full"></div>
            <div className="absolute -bottom-12 -left-12 w-32 h-32 bg-white/10 rounded-full"></div>
          </div>

          <div className="relative z-10 p-8 sm:p-12 lg:p-16">
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="text-center mb-12"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-2xl mb-6 backdrop-blur-sm">
                <TbStar className="w-8 h-8 sm:w-10 sm:h-10 text-yellow-300" />
              </div>
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4 text-white">
                🚀 Unlock Your Full Potential!
              </h2>
              <p className="text-xl sm:text-2xl text-blue-100 font-medium max-w-3xl mx-auto leading-relaxed">
                Join thousands of students who are already accelerating their learning with BrainWave Premium
              </p>
            </motion.div>

            {/* Premium Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12">
              {premiumFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 + (0.1 * index) }}
                  className="group bg-white/10 backdrop-blur-md rounded-2xl p-6 sm:p-8 border border-white/20 hover:bg-white/20 transition-all duration-300"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <feature.icon className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                    </div>
                    <h3 className="text-lg sm:text-xl font-bold text-white">{feature.title}</h3>
                  </div>
                  <p className="text-blue-100 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              className="text-center"
            >
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 sm:p-10 border border-white/20 mb-8">
                <h3 className="text-2xl sm:text-3xl font-bold text-white mb-4">
                  Ready to Excel? 🎯
                </h3>
                <p className="text-blue-100 text-lg mb-6 max-w-2xl mx-auto">
                  Start your journey to academic excellence today. Join BrainWave and unlock unlimited access to premium features.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <Link to="/register" className="w-full sm:w-auto">
                    <motion.button
                      className="w-full sm:w-auto px-8 sm:px-12 py-4 sm:py-5 bg-white text-blue-600 rounded-2xl font-bold text-lg sm:text-xl hover:bg-blue-50 transition-all shadow-2xl hover:shadow-3xl flex items-center justify-center space-x-3 group"
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span>Register Now - It's Free!</span>
                      <TbArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
                    </motion.button>
                  </Link>

                  <div className="text-center sm:text-left">
                    <div className="text-white/90 text-sm font-medium">✨ No credit card required</div>
                    <div className="text-blue-200 text-sm">Start learning immediately</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
              </Link>
              
              <Link to="/login">
                <button className="px-8 py-4 border-2 border-white text-white rounded-xl font-bold text-lg hover:bg-white/10 transition-all">
                  Already Have Account?
                </button>
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <button
            onClick={onTryAnother}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            Try Another Quiz
          </button>
          
          <Link to="/">
            <button className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium">
              Back to Home
            </button>
          </Link>
        </motion.div>

        {/* Trial Badge */}
        <div className="text-center mt-8">
          <span className="inline-block bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium">
            🎯 Trial Mode - Register for unlimited access
          </span>
        </div>
      </div>
    </div>
  );
};

export default TrialQuizResult;
