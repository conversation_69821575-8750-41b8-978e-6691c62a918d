{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\trial\\\\TrialRegistrationPrompt.js\";\nimport React from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { TbX, TbBrain, TbBook, TbChartBar, TbMessageCircle, TbUsers, TbStar, TbArrowRight, TbCheck, TbInfinity, TbTrophy, TbBulb } from \"react-icons/tb\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrialRegistrationPrompt = ({\n  isOpen,\n  onClose,\n  trialResult\n}) => {\n  const premiumFeatures = [{\n    icon: TbInfinity,\n    title: \"Unlimited Quizzes\",\n    description: \"Access thousands of quizzes across all subjects and levels\",\n    color: \"text-blue-600\",\n    bg: \"bg-blue-50\"\n  }, {\n    icon: TbBook,\n    title: \"Study Materials\",\n    description: \"Comprehensive notes, videos, and resources for all subjects\",\n    color: \"text-green-600\",\n    bg: \"bg-green-50\"\n  }, {\n    icon: TbBrain,\n    title: \"AI Assistant\",\n    description: \"Get personalized explanations and study recommendations\",\n    color: \"text-purple-600\",\n    bg: \"bg-purple-50\"\n  }, {\n    icon: TbChartBar,\n    title: \"Ranking System\",\n    description: \"Compete with students nationwide and track your progress\",\n    color: \"text-orange-600\",\n    bg: \"bg-orange-50\"\n  }, {\n    icon: TbMessageCircle,\n    title: \"Forum Access\",\n    description: \"Ask questions and help other students in our community\",\n    color: \"text-indigo-600\",\n    bg: \"bg-indigo-50\"\n  }, {\n    icon: TbTrophy,\n    title: \"Achievements\",\n    description: \"Earn badges and rewards for your learning milestones\",\n    color: \"text-yellow-600\",\n    bg: \"bg-yellow-50\"\n  }];\n  const benefits = [\"Track your progress across all subjects\", \"Get detailed performance analytics\", \"Access past papers and exam preparation\", \"Join study groups and discussions\", \"Receive personalized study plans\", \"Get instant feedback and explanations\"];\n  const modalVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 0.8,\n      y: 50\n    },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      y: 0,\n      transition: {\n        type: \"spring\",\n        damping: 25,\n        stiffness: 300\n      }\n    },\n    exit: {\n      opacity: 0,\n      scale: 0.8,\n      y: 50,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n  const overlayVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(AnimatePresence, {\n    children: /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n      variants: overlayVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      exit: \"exit\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute inset-0 bg-black/60 backdrop-blur-sm\",\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"relative w-full max-w-4xl max-h-[90vh] mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden\",\n        variants: modalVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        exit: \"exit\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-4 sm:px-6 py-4 sm:py-6 text-white relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 bg-white/20 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(TbStar, {\n                    className: \"w-6 h-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl sm:text-2xl font-bold\",\n                    children: \"Congratulations! \\uD83C\\uDF89\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-blue-100 text-sm sm:text-base\",\n                    children: \"You've completed your trial quiz\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: onClose,\n                className: \"p-2 hover:bg-white/20 rounded-lg transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), trialResult && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/10 backdrop-blur-sm rounded-xl p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-3 gap-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold\",\n                    children: [trialResult.percentage, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-100\",\n                    children: \"Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold\",\n                    children: [trialResult.correctAnswers, \"/\", trialResult.totalQuestions]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-100\",\n                    children: \"Correct\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold\",\n                    children: trialResult.passed ? 'PASSED' : 'FAILED'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-100\",\n                    children: \"Result\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 sm:p-6 overflow-y-auto max-h-[60vh]\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBulb, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-gray-800 mb-2\",\n              children: \"Ready to Unlock Your Full Potential?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-lg\",\n              children: \"Join thousands of students who are already excelling with BrainWave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8\",\n            children: premiumFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.3,\n                delay: 0.1 * index\n              },\n              className: `p-4 rounded-xl border-2 border-gray-100 hover:border-blue-200 transition-all ${feature.bg}`,\n              children: [/*#__PURE__*/_jsxDEV(feature.icon, {\n                className: `w-8 h-8 ${feature.color} mb-3`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold text-gray-800 mb-2\",\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-xl p-6 mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-bold text-gray-800 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-5 h-5 text-green-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), \"What You'll Get With Full Access:\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n              children: benefits.map((benefit, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4 text-green-600 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: benefit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 rounded-xl p-6 mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-8 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-600\",\n                  children: \"10,000+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Active Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-600\",\n                  children: \"50,000+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Quizzes Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold text-blue-600\",\n                  children: \"95%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Success Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/register\",\n              className: \"block\",\n              children: /*#__PURE__*/_jsxDEV(motion.button, {\n                className: \"w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl flex items-center justify-center space-x-2\",\n                whileHover: {\n                  scale: 1.02\n                },\n                whileTap: {\n                  scale: 0.98\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Create Free Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TbArrowRight, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/login\",\n              className: \"block\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"w-full py-3 px-6 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all\",\n                children: \"Already have an account? Sign In\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: onClose,\n                className: \"text-gray-500 hover:text-gray-700 text-sm font-medium\",\n                children: \"Maybe later\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 text-center text-xs text-gray-500\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2705 Free to join \\u2022 \\u2705 No credit card required \\u2022 \\u2705 Cancel anytime\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_c = TrialRegistrationPrompt;\nexport default TrialRegistrationPrompt;\nvar _c;\n$RefreshReg$(_c, \"TrialRegistrationPrompt\");", "map": {"version": 3, "names": ["React", "motion", "AnimatePresence", "Link", "TbX", "TbBrain", "TbBook", "TbChartBar", "TbMessageCircle", "TbUsers", "TbStar", "TbArrowRight", "TbCheck", "TbInfinity", "TbTrophy", "TbBulb", "jsxDEV", "_jsxDEV", "TrialRegistrationPrompt", "isOpen", "onClose", "trialResult", "premiumFeatures", "icon", "title", "description", "color", "bg", "benefits", "modalVariants", "hidden", "opacity", "scale", "y", "visible", "transition", "type", "damping", "stiffness", "exit", "duration", "overlayVariants", "children", "div", "className", "variants", "initial", "animate", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "percentage", "correctAnswers", "totalQuestions", "passed", "map", "feature", "index", "delay", "benefit", "to", "button", "whileHover", "whileTap", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/trial/TrialRegistrationPrompt.js"], "sourcesContent": ["import React from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Link } from \"react-router-dom\";\nimport { \n  TbX, \n  TbBrain, \n  TbBook, \n  TbChartBar, \n  TbMessageCircle, \n  TbUsers,\n  TbStar,\n  TbArrowRight,\n  TbCheck,\n  TbInfinity,\n  TbTrophy,\n  TbBulb\n} from \"react-icons/tb\";\n\nconst TrialRegistrationPrompt = ({ isOpen, onClose, trialResult }) => {\n  const premiumFeatures = [\n    {\n      icon: TbInfinity,\n      title: \"Unlimited Quizzes\",\n      description: \"Access thousands of quizzes across all subjects and levels\",\n      color: \"text-blue-600\",\n      bg: \"bg-blue-50\"\n    },\n    {\n      icon: TbBook,\n      title: \"Study Materials\",\n      description: \"Comprehensive notes, videos, and resources for all subjects\",\n      color: \"text-green-600\",\n      bg: \"bg-green-50\"\n    },\n    {\n      icon: TbBrain,\n      title: \"AI Assistant\",\n      description: \"Get personalized explanations and study recommendations\",\n      color: \"text-purple-600\",\n      bg: \"bg-purple-50\"\n    },\n    {\n      icon: Tb<PERSON>hartBar,\n      title: \"Ranking System\",\n      description: \"Compete with students nationwide and track your progress\",\n      color: \"text-orange-600\",\n      bg: \"bg-orange-50\"\n    },\n    {\n      icon: TbMessageCircle,\n      title: \"Forum Access\",\n      description: \"Ask questions and help other students in our community\",\n      color: \"text-indigo-600\",\n      bg: \"bg-indigo-50\"\n    },\n    {\n      icon: TbTrophy,\n      title: \"Achievements\",\n      description: \"Earn badges and rewards for your learning milestones\",\n      color: \"text-yellow-600\",\n      bg: \"bg-yellow-50\"\n    }\n  ];\n\n  const benefits = [\n    \"Track your progress across all subjects\",\n    \"Get detailed performance analytics\",\n    \"Access past papers and exam preparation\",\n    \"Join study groups and discussions\",\n    \"Receive personalized study plans\",\n    \"Get instant feedback and explanations\"\n  ];\n\n  const modalVariants = {\n    hidden: { opacity: 0, scale: 0.8, y: 50 },\n    visible: { \n      opacity: 1, \n      scale: 1, \n      y: 0,\n      transition: { \n        type: \"spring\", \n        damping: 25, \n        stiffness: 300 \n      }\n    },\n    exit: { \n      opacity: 0, \n      scale: 0.8, \n      y: 50,\n      transition: { duration: 0.2 }\n    }\n  };\n\n  const overlayVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n    exit: { opacity: 0 }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n        variants={overlayVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n        exit=\"exit\"\n      >\n        {/* Backdrop */}\n        <motion.div\n          className=\"absolute inset-0 bg-black/60 backdrop-blur-sm\"\n          onClick={onClose}\n        />\n        \n        {/* Modal */}\n        <motion.div\n          className=\"relative w-full max-w-4xl max-h-[90vh] mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden\"\n          variants={modalVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"exit\"\n        >\n          {/* Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 px-4 sm:px-6 py-4 sm:py-6 text-white relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90\"></div>\n            <div className=\"relative z-10\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-white/20 rounded-lg\">\n                    <TbStar className=\"w-6 h-6\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-xl sm:text-2xl font-bold\">Congratulations! 🎉</h2>\n                    <p className=\"text-blue-100 text-sm sm:text-base\">You've completed your trial quiz</p>\n                  </div>\n                </div>\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-white/20 rounded-lg transition-colors\"\n                >\n                  <TbX className=\"w-5 h-5\" />\n                </button>\n              </div>\n\n              {/* Trial Result Summary */}\n              {trialResult && (\n                <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-4\">\n                  <div className=\"grid grid-cols-3 gap-4 text-center\">\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.percentage}%</div>\n                      <div className=\"text-sm text-blue-100\">Score</div>\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.correctAnswers}/{trialResult.totalQuestions}</div>\n                      <div className=\"text-sm text-blue-100\">Correct</div>\n                    </div>\n                    <div>\n                      <div className=\"text-2xl font-bold\">{trialResult.passed ? 'PASSED' : 'FAILED'}</div>\n                      <div className=\"text-sm text-blue-100\">Result</div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4 sm:p-6 overflow-y-auto max-h-[60vh]\">\n            {/* Unlock Message */}\n            <div className=\"text-center mb-8\">\n              <div className=\"w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\">\n                <TbBulb className=\"w-8 h-8 text-white\" />\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-800 mb-2\">\n                Ready to Unlock Your Full Potential?\n              </h3>\n              <p className=\"text-gray-600 text-lg\">\n                Join thousands of students who are already excelling with BrainWave\n              </p>\n            </div>\n\n            {/* Premium Features Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8\">\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: 0.1 * index }}\n                  className={`p-4 rounded-xl border-2 border-gray-100 hover:border-blue-200 transition-all ${feature.bg}`}\n                >\n                  <feature.icon className={`w-8 h-8 ${feature.color} mb-3`} />\n                  <h4 className=\"font-semibold text-gray-800 mb-2\">{feature.title}</h4>\n                  <p className=\"text-sm text-gray-600\">{feature.description}</p>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Benefits List */}\n            <div className=\"bg-gray-50 rounded-xl p-6 mb-8\">\n              <h4 className=\"font-bold text-gray-800 mb-4 flex items-center\">\n                <TbCheck className=\"w-5 h-5 text-green-600 mr-2\" />\n                What You'll Get With Full Access:\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                {benefits.map((benefit, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <TbCheck className=\"w-4 h-4 text-green-600 flex-shrink-0\" />\n                    <span className=\"text-sm text-gray-700\">{benefit}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Social Proof */}\n            <div className=\"bg-blue-50 rounded-xl p-6 mb-8\">\n              <div className=\"flex items-center justify-center space-x-8 text-center\">\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">10,000+</div>\n                  <div className=\"text-sm text-gray-600\">Active Students</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">50,000+</div>\n                  <div className=\"text-sm text-gray-600\">Quizzes Completed</div>\n                </div>\n                <div>\n                  <div className=\"text-2xl font-bold text-blue-600\">95%</div>\n                  <div className=\"text-sm text-gray-600\">Success Rate</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"space-y-4\">\n              <Link to=\"/register\" className=\"block\">\n                <motion.button\n                  className=\"w-full py-4 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg hover:shadow-xl flex items-center justify-center space-x-2\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span>Create Free Account</span>\n                  <TbArrowRight className=\"w-5 h-5\" />\n                </motion.button>\n              </Link>\n\n              <Link to=\"/login\" className=\"block\">\n                <button className=\"w-full py-3 px-6 border-2 border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all\">\n                  Already have an account? Sign In\n                </button>\n              </Link>\n\n              <div className=\"text-center\">\n                <button\n                  onClick={onClose}\n                  className=\"text-gray-500 hover:text-gray-700 text-sm font-medium\"\n                >\n                  Maybe later\n                </button>\n              </div>\n            </div>\n\n            {/* Trust Indicators */}\n            <div className=\"mt-6 text-center text-xs text-gray-500\">\n              <p>✅ Free to join • ✅ No credit card required • ✅ Cancel anytime</p>\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n};\n\nexport default TrialRegistrationPrompt;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,UAAU,EACVC,eAAe,EACfC,OAAO,EACPC,MAAM,EACNC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,MAAM,QACD,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,uBAAuB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAY,CAAC,KAAK;EACpE,MAAMC,eAAe,GAAG,CACtB;IACEC,IAAI,EAAEV,UAAU;IAChBW,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,4DAA4D;IACzEC,KAAK,EAAE,eAAe;IACtBC,EAAE,EAAE;EACN,CAAC,EACD;IACEJ,IAAI,EAAEjB,MAAM;IACZkB,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,6DAA6D;IAC1EC,KAAK,EAAE,gBAAgB;IACvBC,EAAE,EAAE;EACN,CAAC,EACD;IACEJ,IAAI,EAAElB,OAAO;IACbmB,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,yDAAyD;IACtEC,KAAK,EAAE,iBAAiB;IACxBC,EAAE,EAAE;EACN,CAAC,EACD;IACEJ,IAAI,EAAEhB,UAAU;IAChBiB,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,0DAA0D;IACvEC,KAAK,EAAE,iBAAiB;IACxBC,EAAE,EAAE;EACN,CAAC,EACD;IACEJ,IAAI,EAAEf,eAAe;IACrBgB,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,wDAAwD;IACrEC,KAAK,EAAE,iBAAiB;IACxBC,EAAE,EAAE;EACN,CAAC,EACD;IACEJ,IAAI,EAAET,QAAQ;IACdU,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,sDAAsD;IACnEC,KAAK,EAAE,iBAAiB;IACxBC,EAAE,EAAE;EACN,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf,yCAAyC,EACzC,oCAAoC,EACpC,yCAAyC,EACzC,mCAAmC,EACnC,kCAAkC,EAClC,uCAAuC,CACxC;EAED,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAG,CAAC;IACzCC,OAAO,EAAE;MACPH,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC;MACRC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QACVC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE;MACb;IACF,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,GAAG;MACVC,CAAC,EAAE,EAAE;MACLE,UAAU,EAAE;QAAEK,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBX,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBG,OAAO,EAAE;MAAEH,OAAO,EAAE;IAAE,CAAC;IACvBQ,IAAI,EAAE;MAAER,OAAO,EAAE;IAAE;EACrB,CAAC;EAED,IAAI,CAACZ,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA,CAACf,eAAe;IAAAwC,QAAA,eACdzB,OAAA,CAAChB,MAAM,CAAC0C,GAAG;MACTC,SAAS,EAAC,yDAAyD;MACnEC,QAAQ,EAAEJ,eAAgB;MAC1BK,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,SAAS;MACjBR,IAAI,EAAC,MAAM;MAAAG,QAAA,gBAGXzB,OAAA,CAAChB,MAAM,CAAC0C,GAAG;QACTC,SAAS,EAAC,+CAA+C;QACzDI,OAAO,EAAE5B;MAAQ;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAGFnC,OAAA,CAAChB,MAAM,CAAC0C,GAAG;QACTC,SAAS,EAAC,6FAA6F;QACvGC,QAAQ,EAAEhB,aAAc;QACxBiB,OAAO,EAAC,QAAQ;QAChBC,OAAO,EAAC,SAAS;QACjBR,IAAI,EAAC,MAAM;QAAAG,QAAA,gBAGXzB,OAAA;UAAK2B,SAAS,EAAC,4GAA4G;UAAAF,QAAA,gBACzHzB,OAAA;YAAK2B,SAAS,EAAC;UAAqE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3FnC,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAF,QAAA,gBAC5BzB,OAAA;cAAK2B,SAAS,EAAC,wCAAwC;cAAAF,QAAA,gBACrDzB,OAAA;gBAAK2B,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,gBAC1CzB,OAAA;kBAAK2B,SAAS,EAAC,4BAA4B;kBAAAF,QAAA,eACzCzB,OAAA,CAACP,MAAM;oBAACkC,SAAS,EAAC;kBAAS;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNnC,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAI2B,SAAS,EAAC,+BAA+B;oBAAAF,QAAA,EAAC;kBAAmB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtEnC,OAAA;oBAAG2B,SAAS,EAAC,oCAAoC;oBAAAF,QAAA,EAAC;kBAAgC;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnC,OAAA;gBACE+B,OAAO,EAAE5B,OAAQ;gBACjBwB,SAAS,EAAC,oDAAoD;gBAAAF,QAAA,eAE9DzB,OAAA,CAACb,GAAG;kBAACwC,SAAS,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAGL/B,WAAW,iBACVJ,OAAA;cAAK2B,SAAS,EAAC,6CAA6C;cAAAF,QAAA,eAC1DzB,OAAA;gBAAK2B,SAAS,EAAC,oCAAoC;gBAAAF,QAAA,gBACjDzB,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAK2B,SAAS,EAAC,oBAAoB;oBAAAF,QAAA,GAAErB,WAAW,CAACgC,UAAU,EAAC,GAAC;kBAAA;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnEnC,OAAA;oBAAK2B,SAAS,EAAC,uBAAuB;oBAAAF,QAAA,EAAC;kBAAK;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNnC,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAK2B,SAAS,EAAC,oBAAoB;oBAAAF,QAAA,GAAErB,WAAW,CAACiC,cAAc,EAAC,GAAC,EAACjC,WAAW,CAACkC,cAAc;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnGnC,OAAA;oBAAK2B,SAAS,EAAC,uBAAuB;oBAAAF,QAAA,EAAC;kBAAO;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACNnC,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAK2B,SAAS,EAAC,oBAAoB;oBAAAF,QAAA,EAAErB,WAAW,CAACmC,MAAM,GAAG,QAAQ,GAAG;kBAAQ;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpFnC,OAAA;oBAAK2B,SAAS,EAAC,uBAAuB;oBAAAF,QAAA,EAAC;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnC,OAAA;UAAK2B,SAAS,EAAC,yCAAyC;UAAAF,QAAA,gBAEtDzB,OAAA;YAAK2B,SAAS,EAAC,kBAAkB;YAAAF,QAAA,gBAC/BzB,OAAA;cAAK2B,SAAS,EAAC,mHAAmH;cAAAF,QAAA,eAChIzB,OAAA,CAACF,MAAM;gBAAC6B,SAAS,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNnC,OAAA;cAAI2B,SAAS,EAAC,uCAAuC;cAAAF,QAAA,EAAC;YAEtD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnC,OAAA;cAAG2B,SAAS,EAAC,uBAAuB;cAAAF,QAAA,EAAC;YAErC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNnC,OAAA;YAAK2B,SAAS,EAAC,2DAA2D;YAAAF,QAAA,EACvEpB,eAAe,CAACmC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAClC1C,OAAA,CAAChB,MAAM,CAAC0C,GAAG;cAETG,OAAO,EAAE;gBAAEf,OAAO,EAAE,CAAC;gBAAEE,CAAC,EAAE;cAAG,CAAE;cAC/Bc,OAAO,EAAE;gBAAEhB,OAAO,EAAE,CAAC;gBAAEE,CAAC,EAAE;cAAE,CAAE;cAC9BE,UAAU,EAAE;gBAAEK,QAAQ,EAAE,GAAG;gBAAEoB,KAAK,EAAE,GAAG,GAAGD;cAAM,CAAE;cAClDf,SAAS,EAAG,gFAA+Ec,OAAO,CAAC/B,EAAG,EAAE;cAAAe,QAAA,gBAExGzB,OAAA,CAACyC,OAAO,CAACnC,IAAI;gBAACqB,SAAS,EAAG,WAAUc,OAAO,CAAChC,KAAM;cAAO;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DnC,OAAA;gBAAI2B,SAAS,EAAC,kCAAkC;gBAAAF,QAAA,EAAEgB,OAAO,CAAClC;cAAK;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrEnC,OAAA;gBAAG2B,SAAS,EAAC,uBAAuB;gBAAAF,QAAA,EAAEgB,OAAO,CAACjC;cAAW;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GARzDO,KAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASA,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNnC,OAAA;YAAK2B,SAAS,EAAC,gCAAgC;YAAAF,QAAA,gBAC7CzB,OAAA;cAAI2B,SAAS,EAAC,gDAAgD;cAAAF,QAAA,gBAC5DzB,OAAA,CAACL,OAAO;gBAACgC,SAAS,EAAC;cAA6B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,qCAErD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnC,OAAA;cAAK2B,SAAS,EAAC,uCAAuC;cAAAF,QAAA,EACnDd,QAAQ,CAAC6B,GAAG,CAAC,CAACI,OAAO,EAAEF,KAAK,kBAC3B1C,OAAA;gBAAiB2B,SAAS,EAAC,6BAA6B;gBAAAF,QAAA,gBACtDzB,OAAA,CAACL,OAAO;kBAACgC,SAAS,EAAC;gBAAsC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DnC,OAAA;kBAAM2B,SAAS,EAAC,uBAAuB;kBAAAF,QAAA,EAAEmB;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAFhDO,KAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAK2B,SAAS,EAAC,gCAAgC;YAAAF,QAAA,eAC7CzB,OAAA;cAAK2B,SAAS,EAAC,wDAAwD;cAAAF,QAAA,gBACrEzB,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAK2B,SAAS,EAAC,kCAAkC;kBAAAF,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/DnC,OAAA;kBAAK2B,SAAS,EAAC,uBAAuB;kBAAAF,QAAA,EAAC;gBAAe;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNnC,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAK2B,SAAS,EAAC,kCAAkC;kBAAAF,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/DnC,OAAA;kBAAK2B,SAAS,EAAC,uBAAuB;kBAAAF,QAAA,EAAC;gBAAiB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACNnC,OAAA;gBAAAyB,QAAA,gBACEzB,OAAA;kBAAK2B,SAAS,EAAC,kCAAkC;kBAAAF,QAAA,EAAC;gBAAG;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3DnC,OAAA;kBAAK2B,SAAS,EAAC,uBAAuB;kBAAAF,QAAA,EAAC;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAK2B,SAAS,EAAC,WAAW;YAAAF,QAAA,gBACxBzB,OAAA,CAACd,IAAI;cAAC2D,EAAE,EAAC,WAAW;cAAClB,SAAS,EAAC,OAAO;cAAAF,QAAA,eACpCzB,OAAA,CAAChB,MAAM,CAAC8D,MAAM;gBACZnB,SAAS,EAAC,mOAAmO;gBAC7OoB,UAAU,EAAE;kBAAEhC,KAAK,EAAE;gBAAK,CAAE;gBAC5BiC,QAAQ,EAAE;kBAAEjC,KAAK,EAAE;gBAAK,CAAE;gBAAAU,QAAA,gBAE1BzB,OAAA;kBAAAyB,QAAA,EAAM;gBAAmB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChCnC,OAAA,CAACN,YAAY;kBAACiC,SAAS,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEPnC,OAAA,CAACd,IAAI;cAAC2D,EAAE,EAAC,QAAQ;cAAClB,SAAS,EAAC,OAAO;cAAAF,QAAA,eACjCzB,OAAA;gBAAQ2B,SAAS,EAAC,gHAAgH;gBAAAF,QAAA,EAAC;cAEnI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEPnC,OAAA;cAAK2B,SAAS,EAAC,aAAa;cAAAF,QAAA,eAC1BzB,OAAA;gBACE+B,OAAO,EAAE5B,OAAQ;gBACjBwB,SAAS,EAAC,uDAAuD;gBAAAF,QAAA,EAClE;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnC,OAAA;YAAK2B,SAAS,EAAC,wCAAwC;YAAAF,QAAA,eACrDzB,OAAA;cAAAyB,QAAA,EAAG;YAA6D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB,CAAC;AAACc,EAAA,GA9PIhD,uBAAuB;AAgQ7B,eAAeA,uBAAuB;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}